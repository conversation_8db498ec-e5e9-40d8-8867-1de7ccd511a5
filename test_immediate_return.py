#!/usr/bin/env python3
"""
Test script for immediate return functionality
Tests that the C++ AddNodesBulk function can be called without crashing
"""

import socket
import json
import time

def test_immediate_return():
    """Test the immediate return functionality"""
    print("🚀 Testing immediate return functionality...")
    
    try:
        # Connect to Unreal Engine socket server
        print("📡 Connecting to Unreal Engine on localhost:9877...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('localhost', 9877))
        print("✅ Connected successfully!")
        
        # Test command - should return success immediately
        test_command = {
            "command": "add_nodes_bulk",
            "blueprint_path": "/Game/TestBlueprint",
            "function_id": "EventGraph",
            "nodes": [
                {
                    "node_type": "Add_DoubleDouble",
                    "node_id": "test-node-1",
                    "position": {"x": 100, "y": 200}
                }
            ]
        }
        
        print("📤 Sending test command...")
        message = json.dumps(test_command) + '\n'
        sock.send(message.encode('utf-8'))
        
        print("📥 Waiting for response...")
        response = sock.recv(4096).decode('utf-8')
        sock.close()
        
        print(f"📋 Response received: {response}")
        
        # Parse response
        try:
            response_data = json.loads(response)
            if response_data.get('success'):
                print("✅ SUCCESS: C++ function called successfully!")
                print(f"✅ Message: {response_data.get('message', 'No message')}")
                return True
            else:
                print(f"❌ FAILED: {response_data.get('error', 'Unknown error')}")
                return False
        except json.JSONDecodeError:
            print(f"❌ FAILED: Invalid JSON response: {response}")
            return False
            
    except socket.timeout:
        print("❌ TIMEOUT: Unreal Engine not responding")
        return False
    except ConnectionRefusedError:
        print("❌ CONNECTION REFUSED: Is Unreal Engine running with the plugin?")
        return False
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🎯 IMMEDIATE RETURN TEST")
    print("=" * 50)
    
    success = test_immediate_return()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 TEST PASSED: Ready for full node creation implementation!")
    else:
        print("💥 TEST FAILED: Need to debug further")
    
    print("=" * 50)
