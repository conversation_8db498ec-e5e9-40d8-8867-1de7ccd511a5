#!/usr/bin/env python3
"""
Test the C++ function directly with minimal data.
"""

import json
import socket
import time

def test_simple_cpp_call():
    """Test the C++ AddNodesBulk function with the simplest possible data."""
    
    print("🧪 Testing Simple C++ Call")
    print("=" * 30)
    
    try:
        # Connect to Unreal Engine
        print("🔌 Connecting to Unreal Engine...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(60)
        sock.connect(('127.0.0.1', 9877))
        print("✅ Connected!")
        
        # First, create a function to get a valid function_id
        print("📤 Creating function...")
        create_request = {
            "type": "add_function",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_name": "SimpleCppTest",
            "inputs": [],
            "outputs": []
        }
        
        request_json = json.dumps(create_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        response_data = sock.recv(8192).decode('utf-8')
        response = json.loads(response_data)
        
        if not response.get("success"):
            print(f"❌ Function creation failed: {response}")
            return False
            
        function_id = response.get("function_id")
        print(f"✅ Function created with ID: {function_id}")
        
        # Now test the simplest possible node creation
        print("📤 Testing simplest node creation...")
        
        # Use the most basic node data possible
        nodes_request = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_id": function_id,
            "nodes": [
                {
                    "node_type": "PrintString",
                    "node_id": "test1",
                    "node_position": [0, 0],
                    "node_properties": {}
                }
            ]
        }
        
        print(f"📋 Request data: {json.dumps(nodes_request, indent=2)}")
        
        request_json = json.dumps(nodes_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        print("⏳ Waiting for response...")
        
        try:
            response_data = sock.recv(8192).decode('utf-8')
            print(f"📥 Raw response: {response_data}")
            
            response = json.loads(response_data)
            print(f"📊 Parsed response: {response}")
            
            if response.get("success"):
                print("✅ Node creation succeeded!")
                return True
            else:
                print(f"❌ Node creation failed: {response}")
                return False
                
        except socket.error as e:
            print(f"❌ Socket error (connection aborted): {e}")
            print("   This indicates the C++ function crashed")
            return False
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"   Raw response was: {response_data}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    test_simple_cpp_call()
