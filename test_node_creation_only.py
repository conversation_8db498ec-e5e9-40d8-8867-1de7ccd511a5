#!/usr/bin/env python3
"""
Test ONLY node creation - no connections.
Break down the problem into smaller pieces.
"""

import json
import socket
import time

def test_node_creation_only():
    """Test just node creation without any connection logic."""
    
    print("🧪 Testing ONLY Node Creation")
    print("=" * 35)
    
    try:
        # Connect to Unreal Engine
        print("🔌 Connecting to Unreal Engine...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(60)
        sock.connect(('127.0.0.1', 9877))
        print("✅ Connected!")
        
        # Create a function
        print("📤 Creating function...")
        create_request = {
            "type": "add_function",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_name": "NodeCreationTest",
            "inputs": [{"name": "A", "type": "float"}, {"name": "B", "type": "float"}],
            "outputs": [{"name": "Sum", "type": "float"}]
        }
        
        request_json = json.dumps(create_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        response_data = sock.recv(8192).decode('utf-8')
        response = json.loads(response_data)
        
        if not response.get("success"):
            print(f"❌ Function creation failed: {response}")
            return False
            
        function_id = response.get("function_id")
        print(f"✅ Function created with ID: {function_id}")
        
        # Test 1: Create a single PrintString node
        print("\n📤 Test 1: Creating PrintString node...")
        nodes_request = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_id": function_id,
            "nodes": [
                {
                    "node_type": "PrintString",
                    "node_id": "print_test",
                    "node_position": [100, 100],
                    "node_properties": {}
                }
            ]
        }
        
        request_json = json.dumps(nodes_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        try:
            response_data = sock.recv(8192).decode('utf-8')
            response = json.loads(response_data)
            
            if response.get("success"):
                print("✅ PrintString node created successfully!")
                created_nodes = response.get("created_nodes", [])
                print(f"   Created nodes: {created_nodes}")
            else:
                print(f"❌ PrintString node creation failed: {response}")
                return False
        except Exception as e:
            print(f"❌ PrintString node creation crashed: {e}")
            return False
            
        # Test 2: Create an Add_DoubleDouble node
        print("\n📤 Test 2: Creating Add_DoubleDouble node...")
        
        # Reconnect for safety
        sock.close()
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(60)
        sock.connect(('127.0.0.1', 9877))
        
        nodes_request = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_id": function_id,
            "nodes": [
                {
                    "node_type": "Add_DoubleDouble",
                    "node_id": "add_test",
                    "node_position": [200, 100],
                    "node_properties": {}
                }
            ]
        }
        
        request_json = json.dumps(nodes_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        try:
            response_data = sock.recv(8192).decode('utf-8')
            response = json.loads(response_data)
            
            if response.get("success"):
                print("✅ Add_DoubleDouble node created successfully!")
                created_nodes = response.get("created_nodes", [])
                print(f"   Created nodes: {created_nodes}")
            else:
                print(f"❌ Add_DoubleDouble node creation failed: {response}")
        except Exception as e:
            print(f"❌ Add_DoubleDouble node creation crashed: {e}")
            
        # Test 3: Create a Return node
        print("\n📤 Test 3: Creating K2Node_FunctionResult node...")
        
        # Reconnect for safety
        sock.close()
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(60)
        sock.connect(('127.0.0.1', 9877))
        
        nodes_request = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_id": function_id,
            "nodes": [
                {
                    "node_type": "K2Node_FunctionResult",
                    "node_id": "return_test",
                    "node_position": [300, 100],
                    "node_properties": {}
                }
            ]
        }
        
        request_json = json.dumps(nodes_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        try:
            response_data = sock.recv(8192).decode('utf-8')
            response = json.loads(response_data)
            
            if response.get("success"):
                print("✅ K2Node_FunctionResult node created successfully!")
                created_nodes = response.get("created_nodes", [])
                print(f"   Created nodes: {created_nodes}")
            else:
                print(f"❌ K2Node_FunctionResult node creation failed: {response}")
        except Exception as e:
            print(f"❌ K2Node_FunctionResult node creation crashed: {e}")
            
        sock.close()
        
        print("\n🎯 Node Creation Test Summary:")
        print("   - If all 3 tests passed, node creation is working")
        print("   - Next step: Test pin discovery")
        print("   - Then: Test simple connections")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_node_creation_only()
