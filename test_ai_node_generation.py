#!/usr/bin/env python3
"""
Test script for AI-driven Blueprint node generation system.
This script tests the enhanced automatic node creation capabilities.
"""

import json
import websockets
import asyncio
import sys

async def test_ai_node_generation():
    """Test the AI-driven node generation system"""
    
    print("🤖 Testing AI-Driven Blueprint Node Generation System")
    print("=" * 60)
    
    try:
        # Connect to MCP server
        uri = "ws://localhost:8000"
        print(f"Connecting to MCP server at {uri}...")
        
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to MCP server")
            
            # Test the AI-driven automatic node creation
            test_message = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": "test_automatic_node_creation",
                    "arguments": {}
                }
            }
            
            print("\n🧪 Running AI-driven node creation test...")
            await websocket.send(json.dumps(test_message))
            
            response = await websocket.recv()
            result = json.loads(response)
            
            print("\n📊 Test Results:")
            print("-" * 40)
            
            if result.get("result"):
                test_result = result["result"]
                if isinstance(test_result, str):
                    # Parse the string result
                    print(f"Raw result: {test_result}")
                    if "✅" in test_result:
                        print("🎉 AI-driven node creation test PASSED!")
                    else:
                        print("❌ AI-driven node creation test FAILED!")
                else:
                    # Structured result
                    success = test_result.get("success", False)
                    message = test_result.get("message", "No message")
                    total_nodes = test_result.get("total_nodes_created", 0)
                    total_connections = test_result.get("total_connections_made", 0)
                    success_rate = test_result.get("success_rate", 0)
                    
                    print(f"Success: {success}")
                    print(f"Message: {message}")
                    print(f"Total Nodes Created: {total_nodes}")
                    print(f"Total Connections Made: {total_connections}")
                    print(f"Success Rate: {success_rate}%")
                    
                    if test_result.get("test_results"):
                        print("\n📋 Individual Test Results:")
                        for i, test in enumerate(test_result["test_results"], 1):
                            status = "✅ PASS" if test.get("success") else "❌ FAIL"
                            test_name = test.get("test_case", f"Test {i}")
                            nodes = test.get("nodes_created", 0)
                            connections = test.get("connections_made", 0)
                            print(f"  {i}. {test_name}: {status} ({nodes} nodes, {connections} connections)")
                            if not test.get("success"):
                                print(f"     Error: {test.get('error', 'Unknown error')}")
                    
                    if success:
                        print("\n🎉 Overall AI-driven system test PASSED!")
                        print("The AI can now intelligently generate Blueprint functions of any type!")
                    else:
                        print("\n⚠️ Overall AI-driven system test needs improvement")
                        print("Some function types may need additional node mappings or logic")
            else:
                print(f"❌ Test failed with error: {result.get('error', 'Unknown error')}")
                
    except Exception as e:
        print(f"❌ Connection error: {e}")
        print("Make sure the MCP server is running on localhost:8000")
        return False
    
    print("\n" + "=" * 60)
    print("🤖 AI-Driven Node Generation Test Complete")
    return True

if __name__ == "__main__":
    print("Starting AI-driven Blueprint node generation test...")
    asyncio.run(test_ai_node_generation())
