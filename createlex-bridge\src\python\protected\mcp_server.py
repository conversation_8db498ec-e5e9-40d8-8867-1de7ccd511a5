import sys
import os
import socket
import json
from pathlib import Path
import asyncio
# Import the local FastMCP version from the same directory
sys.path.insert(0, os.path.dirname(__file__))
from fastmcp import FastMCP
import atexit

# Load environment variables if available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # dotenv is optional

# ----------------------------------------------------------------------
# NOTE: This file runs completely outside of Unreal's Python interpreter.
#       Do NOT import any unreal.* modules here. It's launched as a
#       standalone process by init_unreal.py.
# ----------------------------------------------------------------------

def write_pid_file():
    """
    Create a PID file so the Unreal plugin knows this process is running,
    and deletes it on exit.
    """
    try:
        pid = os.getpid()
        pid_dir = os.path.join(os.path.expanduser("~"), ".unrealgenai")
        os.makedirs(pid_dir, exist_ok=True)
        pid_path = os.path.join(pid_dir, "mcp_server.pid")

        with open(pid_path, "w") as f:
            # Write: <pid>\n<port>
            # We'll default to port 9877 here; Unreal must be listening on the same port.
            f.write(f"{pid}\n9877")

        def cleanup_pid_file():
            try:
                if os.path.exists(pid_path):
                    os.remove(pid_path)
            except:
                pass

        atexit.register(cleanup_pid_file)
        return pid_path

    except Exception as e:
        print(f"Failed to write PID file: {e}", file=sys.stderr)
        return None


# Write PID file on startup
pid_file = write_pid_file()
if pid_file:
    print(f"MCP Server started with PID file at: {pid_file}", file=sys.stderr)


# -----------------------------------------------------------------------------
# Create the FastMCP instance (this publishes whatever endpoints you've defined
# elsewhere in this plugin). "UnrealHandshake" is just an arbitrary identifier.
# -----------------------------------------------------------------------------
mcp = FastMCP("UnrealHandshake")


# -----------------------------------------------------------------------------
# send_to_unreal: open a TCP socket back to Unreal (default localhost:9877),
# send the JSON command, then wait for a complete JSON response. Return the
# parsed JSON as a Python dict/list.
#
# You can override host/port via env vars:
#   UNREAL_HOST (defaults to "localhost")
#   UNREAL_PORT (defaults to "9877")
# -----------------------------------------------------------------------------
def send_to_unreal(command):
    # Allow overriding via environment
    # Use host.docker.internal to access host machine from Docker container
    host = os.environ.get("UNREAL_HOST", "host.docker.internal")
    port = int(os.environ.get("UNREAL_PORT", "9877"))

    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.settimeout(10)  # 10 second timeout
            s.connect((host, port))
        except Exception as e:
            print(f"[mcp_server] ERROR: cannot connect to Unreal at {host}:{port} - {e}", file=sys.stderr)
            return None

        # Send JSON-encoded command
        try:
            json_str = json.dumps(command)
            s.sendall(json_str.encode("utf-8"))
        except Exception as e:
            print(f"[mcp_server] ERROR: failed to send JSON to Unreal - {e}", file=sys.stderr)
            return None

        # Read until we have valid JSON
        buffer_size = 8192
        response_data = b""
        while True:
            try:
                chunk = s.recv(buffer_size)
            except Exception as e:
                print(f"[mcp_server] ERROR: receive failed - {e}", file=sys.stderr)
                return None

            if not chunk:
                break
            response_data += chunk

            # Try parsing
            try:
                text = response_data.decode("utf-8")
                parsed = json.loads(text)
                return parsed
            except json.JSONDecodeError:
                # Not complete JSON yet; keep reading
                continue

        # If we exit loop without valid JSON:
        if response_data:
            try:
                text = response_data.decode("utf-8")
                parsed = json.loads(text)
                return parsed
            except Exception:
                print("[mcp_server] WARNING: received incomplete/invalid JSON from Unreal", file=sys.stderr)
                return None
        else:
            return None


# ---------------------------------------------------------------------
# Example MCP tools that communicate with Unreal Engine
# ---------------------------------------------------------------------

@mcp.tool()
def handshake_test() -> str:
    """Test connection to Unreal Engine with a simple handshake."""
    try:
        response = send_to_unreal({
            "type": "handshake",
            "message": "Hello from MCP Server"
        })
        if response:
            return f"Handshake successful: {response}"
        else:
            return "Handshake failed: No response from Unreal Engine"
    except Exception as e:
        return f"Handshake failed: {str(e)}"

@mcp.tool()
def spawn_actor(actor_class: str, x: float = 0.0, y: float = 0.0, z: float = 0.0) -> str:
    """Spawn an actor in Unreal Engine at the specified location."""
    try:
        response = send_to_unreal({
            "type": "spawn",
            "actor_class": actor_class,
            "location": [x, y, z]
        })
        if response:
            return f"Actor spawned successfully: {response}"
        else:
            return "Failed to spawn actor: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to spawn actor: {str(e)}"

@mcp.tool()
def get_scene_objects() -> str:
    """Get a list of all objects in the current Unreal Engine scene."""
    try:
        response = send_to_unreal({
            "type": "get_all_scene_objects"
        })
        if response:
            return f"Scene objects: {response}"
        else:
            return "Failed to get scene objects: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get scene objects: {str(e)}"

@mcp.tool()
def create_material(material_name: str, base_color: list = [1.0, 1.0, 1.0], metallic: float = 0.0, roughness: float = 0.5) -> str:
    """Create a new material in Unreal Engine."""
    try:
        response = send_to_unreal({
            "type": "create_material",
            "material_name": material_name,
            "base_color": base_color,
            "metallic": metallic,
            "roughness": roughness
        })
        if response:
            return f"Material created successfully: {response}"
        else:
            return "Failed to create material: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to create material: {str(e)}"

@mcp.tool()
def modify_object(actor_name: str, property_name: str, property_value: str) -> str:
    """Modify a property of an object in Unreal Engine."""
    try:
        response = send_to_unreal({
            "type": "modify_object",
            "actor_name": actor_name,
            "property_name": property_name,
            "property_value": property_value
        })
        if response:
            return f"Object modified successfully: {response}"
        else:
            return "Failed to modify object: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to modify object: {str(e)}"

@mcp.tool()
def execute_python(script: str) -> str:
    """Execute Python script in Unreal Engine."""
    try:
        response = send_to_unreal({
            "type": "execute_python",
            "script": script
        })
        if response:
            return f"Python script executed: {response}"
        else:
            return "Failed to execute Python script: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to execute Python script: {str(e)}"

@mcp.tool()
def execute_unreal_command(command: str) -> str:
    """Execute Unreal Engine console command."""
    try:
        response = send_to_unreal({
            "type": "execute_unreal_command",
            "command": command
        })
        if response:
            return f"Unreal command executed: {response}"
        else:
            return "Failed to execute Unreal command: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to execute Unreal command: {str(e)}"

@mcp.tool()
def create_blueprint(blueprint_name: str, parent_class: str = "Actor", save_path: str = "/Game/Blueprints") -> str:
    """Create a new Blueprint in Unreal Engine."""
    try:
        response = send_to_unreal({
            "type": "create_blueprint",
            "blueprint_name": blueprint_name,
            "parent_class": parent_class,
            "save_path": save_path
        })
        if response:
            return f"Blueprint created successfully: {response}"
        else:
            return "Failed to create blueprint: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to create blueprint: {str(e)}"

@mcp.tool()
def add_component(blueprint_path: str, component_type: str, component_name: str) -> str:
    """Add a component to a Blueprint."""
    try:
        response = send_to_unreal({
            "type": "add_component",
            "blueprint_path": blueprint_path,
            "component_type": component_type,
            "component_name": component_name
        })
        if response:
            return f"Component added successfully: {response}"
        else:
            return "Failed to add component: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add component: {str(e)}"

@mcp.tool()
def add_variable(blueprint_path: str, variable_name: str, variable_type: str, default_value: str = "") -> str:
    """Add a variable to a Blueprint."""
    try:
        response = send_to_unreal({
            "type": "add_variable",
            "blueprint_path": blueprint_path,
            "variable_name": variable_name,
            "variable_type": variable_type,
            "default_value": default_value
        })
        if response:
            return f"Variable added successfully: {response}"
        else:
            return "Failed to add variable: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add variable: {str(e)}"

@mcp.tool()
def add_function(blueprint_path: str, function_name: str, return_type: str = "exec") -> str:
    """Add a function to a Blueprint."""
    try:
        response = send_to_unreal({
            "type": "add_function",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "return_type": return_type
        })
        if response:
            return f"Function added successfully: {response}"
        else:
            return "Failed to add function: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add function: {str(e)}"

@mcp.tool()
def add_node(blueprint_path: str, function_name: str, node_type: str, x: float = 0.0, y: float = 0.0) -> str:
    """Add a node to a Blueprint function."""
    try:
        response = send_to_unreal({
            "type": "add_node",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "node_type": node_type,
            "x": x,
            "y": y
        })
        if response:
            return f"Node added successfully: {response}"
        else:
            return "Failed to add node: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add node: {str(e)}"

@mcp.tool()
def connect_nodes(blueprint_path: str, function_name: str, output_node_guid: str, input_node_guid: str, output_pin: str = "exec", input_pin: str = "exec") -> str:
    """Connect two nodes in a Blueprint function."""
    try:
        response = send_to_unreal({
            "type": "connect_nodes",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "output_node_guid": output_node_guid,
            "input_node_guid": input_node_guid,
            "output_pin": output_pin,
            "input_pin": input_pin
        })
        if response:
            return f"Nodes connected successfully: {response}"
        else:
            return "Failed to connect nodes: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to connect nodes: {str(e)}"

@mcp.tool()
def compile_blueprint(blueprint_path: str) -> str:
    """Compile a Blueprint."""
    try:
        response = send_to_unreal({
            "type": "compile_blueprint",
            "blueprint_path": blueprint_path
        })
        if response:
            return f"Blueprint compiled successfully: {response}"
        else:
            return "Failed to compile blueprint: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to compile blueprint: {str(e)}"

@mcp.tool()
def spawn_blueprint(blueprint_path: str, x: float = 0.0, y: float = 0.0, z: float = 0.0) -> str:
    """Spawn an instance of a Blueprint in the scene."""
    try:
        response = send_to_unreal({
            "type": "spawn_blueprint",
            "blueprint_path": blueprint_path,
            "location": [x, y, z]
        })
        if response:
            return f"Blueprint spawned successfully: {response}"
        else:
            return "Failed to spawn blueprint: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to spawn blueprint: {str(e)}"

@mcp.tool()
def get_all_nodes(blueprint_path: str, function_name: str) -> str:
    """Get all nodes in a Blueprint function."""
    try:
        response = send_to_unreal({
            "type": "get_all_nodes",
            "blueprint_path": blueprint_path,
            "function_name": function_name
        })
        if response:
            return f"Nodes retrieved: {response}"
        else:
            return "Failed to get nodes: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get nodes: {str(e)}"

@mcp.tool()
def get_node_suggestions(search_term: str) -> str:
    """Get node suggestions for Blueprint creation."""
    try:
        response = send_to_unreal({
            "type": "get_node_suggestions",
            "search_term": search_term
        })
        if response:
            return f"Node suggestions: {response}"
        else:
            return "Failed to get node suggestions: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get node suggestions: {str(e)}"

@mcp.tool()
def create_project_folder(folder_path: str) -> str:
    """Create a new folder in the Unreal Engine project."""
    try:
        response = send_to_unreal({
            "type": "create_project_folder",
            "folder_path": folder_path
        })
        if response:
            return f"Folder created successfully: {response}"
        else:
            return "Failed to create folder: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to create folder: {str(e)}"

@mcp.tool()
def get_files_in_folder(folder_path: str) -> str:
    """Get list of files in a project folder."""
    try:
        response = send_to_unreal({
            "type": "get_files_in_folder",
            "folder_path": folder_path
        })
        if response:
            return f"Files in folder: {response}"
        else:
            return "Failed to get files: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get files: {str(e)}"

@mcp.tool()
def delete_node(blueprint_path: str, function_name: str, node_guid: str) -> str:
    """Delete a node from a Blueprint function."""
    try:
        response = send_to_unreal({
            "type": "delete_node",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "node_guid": node_guid
        })
        if response:
            return f"Node deleted successfully: {response}"
        else:
            return "Failed to delete node: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to delete node: {str(e)}"

@mcp.tool()
def get_node_guid(blueprint_path: str, function_name: str, node_type: str) -> str:
    """Get the GUID of a specific node in a Blueprint function."""
    try:
        response = send_to_unreal({
            "type": "get_node_guid",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "node_type": node_type
        })
        if response:
            return f"Node GUID: {response}"
        else:
            return "Failed to get node GUID: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get node GUID: {str(e)}"

@mcp.tool()
def add_nodes_bulk(blueprint_path: str, function_name: str, nodes_data: str) -> str:
    """Add multiple nodes to a Blueprint function in bulk. nodes_data should be JSON string."""
    try:
        import json
        nodes = json.loads(nodes_data)
        response = send_to_unreal({
            "type": "add_nodes_bulk",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "nodes": nodes
        })
        if response:
            return f"Nodes added in bulk: {response}"
        else:
            return "Failed to add nodes in bulk: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add nodes in bulk: {str(e)}"

@mcp.tool()
def connect_nodes_bulk(blueprint_path: str, function_name: str, connections_data: str) -> str:
    """Connect multiple nodes in a Blueprint function in bulk. connections_data should be JSON string."""
    try:
        import json
        connections = json.loads(connections_data)
        response = send_to_unreal({
            "type": "connect_nodes_bulk",
            "blueprint_path": blueprint_path,
            "function_name": function_name,
            "connections": connections
        })
        if response:
            return f"Nodes connected in bulk: {response}"
        else:
            return "Failed to connect nodes in bulk: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to connect nodes in bulk: {str(e)}"

@mcp.tool()
def edit_component_property(actor_name: str, component_name: str, property_name: str, property_value: str) -> str:
    """Edit a property of a specific component on an actor."""
    try:
        response = send_to_unreal({
            "type": "edit_component_property",
            "actor_name": actor_name,
            "component_name": component_name,
            "property_name": property_name,
            "property_value": property_value
        })
        if response:
            return f"Component property edited: {response}"
        else:
            return "Failed to edit component property: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to edit component property: {str(e)}"

@mcp.tool()
def add_component_with_events(actor_name: str, component_type: str, component_name: str, events_data: str = "{}") -> str:
    """Add a component to an actor with event bindings. events_data should be JSON string."""
    try:
        import json
        events = json.loads(events_data)
        response = send_to_unreal({
            "type": "add_component_with_events",
            "actor_name": actor_name,
            "component_type": component_type,
            "component_name": component_name,
            "events": events
        })
        if response:
            return f"Component with events added: {response}"
        else:
            return "Failed to add component with events: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add component with events: {str(e)}"

@mcp.tool()
def add_input_binding(action_name: str, key: str, binding_type: str = "Action") -> str:
    """Add an input binding to the project."""
    try:
        response = send_to_unreal({
            "type": "add_input_binding",
            "action_name": action_name,
            "key": key,
            "binding_type": binding_type
        })
        if response:
            return f"Input binding added: {response}"
        else:
            return "Failed to add input binding: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add input binding: {str(e)}"

@mcp.tool()
def add_widget_to_user_widget(widget_blueprint_path: str, widget_type: str, widget_name: str, x: float = 0.0, y: float = 0.0) -> str:
    """Add a widget to a User Widget Blueprint."""
    try:
        response = send_to_unreal({
            "type": "add_widget_to_user_widget",
            "widget_blueprint_path": widget_blueprint_path,
            "widget_type": widget_type,
            "widget_name": widget_name,
            "x": x,
            "y": y
        })
        if response:
            return f"Widget added to User Widget: {response}"
        else:
            return "Failed to add widget: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to add widget: {str(e)}"

@mcp.tool()
def edit_widget_property(widget_blueprint_path: str, widget_name: str, property_name: str, property_value: str) -> str:
    """Edit a property of a widget in a User Widget Blueprint."""
    try:
        response = send_to_unreal({
            "type": "edit_widget_property",
            "widget_blueprint_path": widget_blueprint_path,
            "widget_name": widget_name,
            "property_name": property_name,
            "property_value": property_value
        })
        if response:
            return f"Widget property edited: {response}"
        else:
            return "Failed to edit widget property: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to edit widget property: {str(e)}"

@mcp.tool()
def server_status() -> str:
    """Get the current status of the MCP server and its connection to Unreal Engine."""
    try:
        # Use the same host configuration as send_to_unreal
        host = os.environ.get("UNREAL_HOST", "host.docker.internal")
        port = int(os.environ.get("UNREAL_PORT", "9877"))
        
        # Test connection
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(5)
            result = s.connect_ex((host, port))
            
        if result == 0:
            connection_status = f"✅ Connected to Unreal Engine at {host}:{port}"
        else:
            connection_status = f"❌ Cannot connect to Unreal Engine at {host}:{port}"
            
        return f"MCP Server Status:\n{connection_status}\nPID: {os.getpid()}\nPID File: {pid_file}"
    except Exception as e:
        return f"Status check failed: {str(e)}"

@mcp.tool()
def get_current_blueprint_context() -> str:
    """Get the current Blueprint context (which Blueprint is currently open/active in the editor)."""
    try:
        response = send_to_unreal({
            "type": "get_current_blueprint_context"
        })
        if response:
            return f"Current Blueprint context: {response}"
        else:
            return "Failed to get Blueprint context: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get Blueprint context: {str(e)}"

@mcp.tool()
def generate_blueprint_function(
    function_name: str, 
    description: str,
    inputs: str = "[]",
    outputs: str = "[]",
    use_current_context: bool = True,
    blueprint_path: str = ""
) -> str:
    """Generate a complete Blueprint function with automatic node creation and wiring.
    
    Args:
        function_name: Name of the function to create
        description: Natural language description of what the function should do
        inputs: JSON string array of input parameters e.g. '[{"name": "Health", "type": "float"}, {"name": "Damage", "type": "float"}]'
        outputs: JSON string array of output parameters e.g. '[{"name": "NewHealth", "type": "float"}]'
        use_current_context: If True, uses the currently open Blueprint. If False, requires blueprint_path
        blueprint_path: Path to the Blueprint to add the function to (only used if use_current_context is False)
    """
    try:
        import json
        inputs_data = json.loads(inputs) if inputs else []
        outputs_data = json.loads(outputs) if outputs else []
        
        response = send_to_unreal({
            "type": "generate_blueprint_function",
            "function_name": function_name,
            "description": description,
            "inputs": inputs_data,
            "outputs": outputs_data,
            "use_current_context": use_current_context,
            "blueprint_path": blueprint_path
        })
        if response:
            return f"Blueprint function generated: {response}"
        else:
            return "Failed to generate Blueprint function: No response from Unreal Engine"
    except json.JSONDecodeError as e:
        return f"Failed to parse JSON inputs/outputs: {str(e)}"
    except Exception as e:
        return f"Failed to generate Blueprint function: {str(e)}"

@mcp.tool()
def analyze_blueprint_graph(
    graph_name: str = "EventGraph",
    use_current_context: bool = True,
    blueprint_path: str = "",
    include_connections: bool = True,
    include_node_details: bool = True
) -> str:
    """Analyze and explain what a Blueprint graph is doing by examining all nodes and connections.
    
    Args:
        graph_name: Name of the graph to analyze (e.g., "EventGraph", "ConstructionScript", or a function name)
        use_current_context: If True, uses the currently open Blueprint. If False, requires blueprint_path
        blueprint_path: Path to the Blueprint to analyze (only used if use_current_context is False)
        include_connections: Include information about how nodes are connected
        include_node_details: Include detailed information about each node's properties
    """
    try:
        response = send_to_unreal({
            "type": "analyze_blueprint_graph",
            "graph_name": graph_name,
            "use_current_context": use_current_context,
            "blueprint_path": blueprint_path,
            "include_connections": include_connections,
            "include_node_details": include_node_details
        })
        if response:
            return f"Blueprint graph analysis: {response}"
        else:
            return "Failed to analyze Blueprint graph: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to analyze Blueprint graph: {str(e)}"

@mcp.tool()
def generate_smart_blueprint_function(
    function_name: str,
    description: str,
    inputs: str = "[]",
    outputs: str = "[]",
    complexity: str = "medium"
) -> str:
    """Generate an intelligent Blueprint function with advanced node creation and wiring.
    
    Args:
        function_name: Name of the function to create
        description: Natural language description of what the function should do
        inputs: JSON string array of input parameters
        outputs: JSON string array of output parameters
        complexity: Function complexity level ("simple", "medium", "complex")
    """
    try:
        response = send_to_unreal({
            "type": "generate_smart_blueprint_function",
            "function_name": function_name,
            "description": description,
            "inputs": inputs,
            "outputs": outputs,
            "complexity": complexity
        })
        if response:
            return f"Smart Blueprint function generated: {response}"
        else:
            return "Failed to generate smart Blueprint function: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to generate smart Blueprint function: {str(e)}"

@mcp.tool()
def get_blueprint_context_detailed() -> str:
    """Get detailed Blueprint context with full analysis capabilities."""
    try:
        response = send_to_unreal({
            "type": "get_blueprint_context_detailed"
        })
        if response:
            return f"Detailed Blueprint context: {response}"
        else:
            return "Failed to get detailed Blueprint context: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to get detailed Blueprint context: {str(e)}"

@mcp.tool()
def analyze_blueprint_graph_advanced(
    graph_name: str = "EventGraph",
    analysis_type: str = "comprehensive",
    include_ai_insights: bool = True
) -> str:
    """Perform advanced Blueprint graph analysis with AI-powered insights.
    
    Args:
        graph_name: Name of the graph to analyze
        analysis_type: Type of analysis ("basic", "comprehensive", "performance", "logic_flow")
        include_ai_insights: Whether to include AI-generated insights and suggestions
    """
    try:
        response = send_to_unreal({
            "type": "analyze_blueprint_graph_advanced",
            "graph_name": graph_name,
            "analysis_type": analysis_type,
            "include_ai_insights": include_ai_insights
        })
        if response:
            return f"Advanced Blueprint graph analysis: {response}"
        else:
            return "Failed to perform advanced Blueprint graph analysis: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to perform advanced Blueprint graph analysis: {str(e)}"

@mcp.tool()
def generate_context_aware_blueprint_function(
    function_description: str,
    function_name: str = "",
    complexity: str = "medium"
) -> str:
    """Generate a context-aware Blueprint function using current Blueprint state and C++ Asset Registry API.
    
    Args:
        function_description: Natural language description of what the function should do
        function_name: Optional custom function name (will be auto-generated if not provided)
        complexity: Function complexity level ("simple", "medium", "complex")
    """
    try:
        response = send_to_unreal({
            "type": "generate_context_aware_blueprint_function",
            "function_description": function_description,
            "function_name": function_name,
            "complexity": complexity
        })
        if response:
            return f"Context-aware Blueprint function generated: {response}"
        else:
            return "Failed to generate context-aware Blueprint function: No response from Unreal Engine"
    except Exception as e:
        return f"Failed to generate context-aware Blueprint function: {str(e)}"

# Add a health check endpoint
# Note: Custom routes may not be available in all FastMCP versions
# The health check functionality is available via the server_status tool instead

# @mcp.custom_route("/health", methods=["GET"])
# async def health_check(request):
#     """Health check endpoint for monitoring."""
#     from starlette.responses import JSONResponse
#     
#     try:
#         host = os.environ.get("UNREAL_HOST", "localhost")
#         port = int(os.environ.get("UNREAL_PORT", "9877"))
#         
#         # Test connection to Unreal Engine
#         with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
#             s.settimeout(2)
#             result = s.connect_ex((host, port))
#             
#         unreal_connected = result == 0
#         
#         return JSONResponse({
#             "status": "healthy" if unreal_connected else "degraded",
#             "mcp_server": "running",
#             "unreal_engine": "connected" if unreal_connected else "disconnected",
#             "unreal_host": host,
#             "unreal_port": port,
#             "pid": os.getpid()
#         })
#     except Exception as e:
#         return JSONResponse({
#             "status": "unhealthy",
#             "error": str(e)
#         }, status_code=500)

def start_server(subscription_data=None):
    """Entry point expected by subscription manager to start the MCP service.
    The optional subscription_data dict can be used for telemetry or rate-limits in future."""
    try:
        print("[mcp_server] Starting via start_server entrypoint", file=sys.stderr)
        if subscription_data:
            import json
            print(f"[mcp_server] Subscription data: {json.dumps(subscription_data)[:200]}…", file=sys.stderr)
        # Start FastMCP WebSocket server
        mcp.run(host="0.0.0.0", port=8000)
    except Exception as e:
        print(f"[mcp_server] ERROR: failed to start FastMCP - {e}", file=sys.stderr)
        sys.exit(1)

# -----------------------------------------------------------------------------
# If you had any "main loop" or long-running logic, it would go here.
# But since FastMCP is event-driven, you don't need a while-loop. Just let
# FastMCP handle incoming HTTP (or WebSocket) requests:
# -----------------------------------------------------------------------------

if __name__ == "__main__":
    start_server()
