#!/usr/bin/env python3
"""
Test script to verify the connection logic fix in CreateLex plugin.
This script tests that the fixed condition allows connections for single nodes.
"""

import socket
import json
import time

def test_connection_fix():
    """Test the connection logic fix by creating a single node and verifying connections are attempted."""
    
    print("🧪 Testing CreateLex Plugin Connection Fix")
    print("=" * 50)
    
    try:
        # Connect to the plugin socket server
        print("📡 Connecting to Unreal Engine plugin on localhost:9877...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('localhost', 9877))
        print("✅ Connected successfully!")
        
        # Test command: Create a simple function with a single node
        test_command = {
            "type": "generate_smart_blueprint_function",
            "blueprint_path": "/Game/TestBlueprints/ConnectionTestBP",
            "function_name": "TestConnectionFix",
            "description": "Test function to verify connection logic works with single nodes",
            "inputs": [
                {"name": "TestInput", "type": "float", "description": "Test input value"}
            ],
            "outputs": [
                {"name": "TestOutput", "type": "float", "description": "Test output value"}
            ],
            "complexity": "simple"
        }
        
        print(f"🚀 Sending test command: {test_command['function_name']}")
        
        # Send the command
        command_json = json.dumps(test_command)
        sock.send(command_json.encode('utf-8'))
        
        # Receive the response
        print("⏳ Waiting for response...")
        response_data = sock.recv(4096)
        response = json.loads(response_data.decode('utf-8'))
        
        print("📋 Response received:")
        print(f"   Success: {response.get('success', False)}")
        print(f"   Function: {response.get('function_name', 'N/A')}")
        print(f"   Nodes Created: {response.get('nodes_created', 0)}")
        print(f"   Connections Made: {response.get('connections_made', 0)}")
        print(f"   Message: {response.get('message', 'N/A')}")
        
        # Analyze the results
        nodes_created = response.get('nodes_created', 0)
        connections_made = response.get('connections_made', 0)
        
        print("\n🔍 Analysis:")
        if nodes_created >= 1:
            print(f"✅ Node creation working: {nodes_created} node(s) created")
            
            if connections_made > 0:
                print(f"✅ CONNECTION FIX SUCCESSFUL: {connections_made} connection(s) made!")
                print("🎉 The fix is working - connections are no longer being skipped!")
            else:
                print("⚠️  No connections made - this could be:")
                print("   - Connection logic still needs refinement")
                print("   - No suitable connection targets found")
                print("   - Connection generation logic needs improvement")
        else:
            print("❌ Node creation failed - cannot test connections")
        
        # Check for specific log messages that indicate the fix
        if response.get('success') and nodes_created >= 1:
            print("\n📊 Test Results:")
            print(f"   ✅ Plugin responds to commands")
            print(f"   ✅ Node creation works ({nodes_created} nodes)")
            if connections_made > 0:
                print(f"   ✅ Connection logic fixed ({connections_made} connections)")
                print("   🎯 FIX VERIFIED: Connection condition now allows single nodes!")
            else:
                print(f"   ⚠️  Connection logic needs further work (0 connections)")
                print("   📝 Next step: Improve connection generation logic")
        
        sock.close()
        return True
        
    except ConnectionRefusedError:
        print("❌ Could not connect to Unreal Engine plugin")
        print("   Make sure Unreal Engine is running with the CreateLex plugin loaded")
        return False
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

def main():
    """Main test function."""
    print("CreateLex Plugin Connection Fix Test")
    print("This test verifies that the connection logic fix allows connections for single nodes")
    print()
    
    success = test_connection_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎯 Test completed - check results above")
    else:
        print("❌ Test failed - ensure Unreal Engine is running with CreateLex plugin")
    print("=" * 50)

if __name__ == "__main__":
    main()
