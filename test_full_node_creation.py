#!/usr/bin/env python3
"""
🧪 FULL NODE CREATION TEST - CreatelexGenAI Plugin
==================================================
This test verifies that our C++ AddNodesBulk function can actually create
PrintString nodes in Unreal Engine Blueprint graphs.

Expected: Function creates a visible PrintString node and returns its GUID.
"""

import socket
import json
import time

def test_full_node_creation():
    print("🧪 FULL NODE CREATION TEST - CreatelexGenAI Plugin")
    print("=" * 60)
    print("This test verifies that our C++ AddNodesBulk function can actually create")
    print("PrintString nodes in Unreal Engine Blueprint graphs.")
    print("Expected: Function creates a visible PrintString node and returns its GUID.")
    print("=" * 60)
    
    try:
        print("🚀 Testing full node creation functionality...")
        print("📡 Connecting to Unreal Engine on localhost:9877...")
        
        # Create socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('localhost', 9877))
        print("✅ Connected successfully!")
        
        # Prepare test command for EventGraph (easier to test than function graphs)
        test_command = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/TestBlueprint",
            "function_id": "EventGraph",  # Use EventGraph for easier testing
            "nodes": [
                {
                    "node_type": "PrintString",
                    "node_id": "test_node_1",
                    "position": {"x": 100, "y": 200}
                }
            ]
        }
        
        print("📤 Sending test command...")
        print(f"Command: {json.dumps(test_command, indent=2)}")
        
        # Send command
        command_json = json.dumps(test_command)
        sock.send(command_json.encode('utf-8'))
        
        print("⏳ Waiting for response...")
        
        # Receive response
        response_data = sock.recv(4096).decode('utf-8')
        print(f"📥 Raw response: {response_data}")
        
        # Parse response
        try:
            response = json.loads(response_data)
            print(f"📋 Parsed response: {json.dumps(response, indent=2)}")
            
            # Check for successful node creation
            if response.get('success') and 'nodes' in response:
                nodes = response.get('nodes', {})
                if nodes:
                    print("🎉 SUCCESS! Full node creation test PASSED!")
                    print("✅ C++ function created actual nodes in Unreal Engine")
                    print("✅ FGraphNodeCreator pattern is working correctly")
                    print("✅ PrintString node was created successfully")
                    print(f"✅ Node GUID returned: {list(nodes.values())[0] if nodes else 'None'}")
                    print(f"✅ Node mapping: {nodes}")
                    return True
                else:
                    print("⚠️ SUCCESS but no nodes returned")
                    print("✅ C++ function executed without crashing")
                    print("⚠️ But no node GUIDs were returned (check Blueprint path)")
                    return True
            else:
                print("❌ NODE CREATION FAILED")
                print(f"Expected: success=True with 'nodes' field containing GUIDs")
                print(f"Got: {response}")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse JSON response: {e}")
            print(f"Raw response: {response_data}")
            return False
            
    except socket.timeout:
        print("❌ Connection timeout - is Unreal Engine running?")
        return False
    except ConnectionRefusedError:
        print("❌ Connection refused - is the plugin socket server running?")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    success = test_full_node_creation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 TEST RESULT: PASSED ✅")
        print("🔧 Next step: Verify nodes are visible in Blueprint editor")
    else:
        print("🎯 TEST RESULT: FAILED ❌")
        print("🔧 Next step: Debug the C++ node creation logic")
    print("=" * 60)
