#!/usr/bin/env python3
"""
Test single node creation to isolate the crash.
"""

import json
import socket
import time

def test_single_node():
    """Test single node creation to see what's causing the crash."""
    
    print("🧪 Testing Single Node Creation")
    print("=" * 35)
    
    try:
        # Connect to Unreal Engine
        print("🔌 Connecting to Unreal Engine...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(60)
        sock.connect(('127.0.0.1', 9877))
        print("✅ Connected!")
        
        # First, create a function
        print("📤 Creating function...")
        create_request = {
            "type": "add_function",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_name": "SingleNodeTest",
            "inputs": [{"name": "A", "type": "float"}],
            "outputs": [{"name": "Result", "type": "float"}]
        }
        
        request_json = json.dumps(create_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        response_data = sock.recv(8192).decode('utf-8')
        response = json.loads(response_data)
        
        if not response.get("success"):
            print(f"❌ Function creation failed: {response}")
            return False
            
        function_id = response.get("function_id")
        print(f"✅ Function created with ID: {function_id}")
        
        # Test 1: Try PrintString node (should work)
        print("📤 Testing PrintString node...")
        nodes_request = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_id": function_id,
            "nodes": [
                {
                    "node_type": "PrintString",
                    "node_id": "print_test",
                    "node_position": [100, 100],
                    "node_properties": {}
                }
            ]
        }
        
        request_json = json.dumps(nodes_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        try:
            response_data = sock.recv(8192).decode('utf-8')
            response = json.loads(response_data)
            
            if response.get("success"):
                print("✅ PrintString node created successfully!")
            else:
                print(f"❌ PrintString node creation failed: {response}")
                return False
        except Exception as e:
            print(f"❌ PrintString node creation crashed: {e}")
            return False
            
        # Test 2: Try Add_DoubleDouble node
        print("📤 Testing Add_DoubleDouble node...")
        
        # Reconnect since the previous test might have closed the connection
        sock.close()
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(60)
        sock.connect(('127.0.0.1', 9877))
        
        nodes_request = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_id": function_id,
            "nodes": [
                {
                    "node_type": "Add_DoubleDouble",
                    "node_id": "add_test",
                    "node_position": [200, 100],
                    "node_properties": {}
                }
            ]
        }
        
        request_json = json.dumps(nodes_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        try:
            response_data = sock.recv(8192).decode('utf-8')
            response = json.loads(response_data)
            
            if response.get("success"):
                print("✅ Add_DoubleDouble node created successfully!")
            else:
                print(f"❌ Add_DoubleDouble node creation failed: {response}")
        except Exception as e:
            print(f"❌ Add_DoubleDouble node creation crashed: {e}")
            
        sock.close()
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_single_node()
