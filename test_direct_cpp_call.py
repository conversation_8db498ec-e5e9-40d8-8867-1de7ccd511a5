#!/usr/bin/env python3
"""
Test calling the C++ function directly from Python without socket server.
This will help isolate if the issue is in the socket server or the C++ function.
"""

import unreal
import json

def test_direct_cpp_call():
    """Test calling the C++ AddNodesBulk function directly."""
    
    print("🧪 Testing Direct C++ Call")
    print("=" * 30)
    
    try:
        # Test 1: Check if the class exists
        print("📤 Test 1: Checking if GenBlueprintNodeCreator class exists...")
        try:
            node_creator = unreal.GenBlueprintNodeCreator
            print(f"✅ GenBlueprintNodeCreator class found: {node_creator}")
        except Exception as e:
            print(f"❌ GenBlueprintNodeCreator class not found: {e}")
            return False
        
        # Test 2: Check if the method exists
        print("📤 Test 2: Checking if add_nodes_bulk method exists...")
        try:
            method = getattr(node_creator, 'add_nodes_bulk', None)
            if method:
                print(f"✅ add_nodes_bulk method found: {method}")
            else:
                print("❌ add_nodes_bulk method not found")
                return False
        except Exception as e:
            print(f"❌ Error checking add_nodes_bulk method: {e}")
            return False
        
        # Test 3: Create a simple function first
        print("📤 Test 3: Creating a simple function...")
        try:
            # Use the existing add_function method
            add_function_method = getattr(node_creator, 'add_function', None)
            if not add_function_method:
                print("❌ add_function method not found")
                return False
            
            blueprint_path = "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson"
            function_name = "DirectCppTest"
            
            function_id = add_function_method(blueprint_path, function_name, "", "")
            if function_id:
                print(f"✅ Function created with ID: {function_id}")
            else:
                print("❌ Function creation failed")
                return False
        except Exception as e:
            print(f"❌ Function creation error: {e}")
            return False
        
        # Test 4: Try calling add_nodes_bulk with minimal data
        print("📤 Test 4: Calling add_nodes_bulk directly...")
        try:
            # Convert function_id to proper GUID format if needed
            formatted_function_id = function_id
            if len(function_id) == 32 and '-' not in function_id:
                formatted_function_id = f"{function_id[:8]}-{function_id[8:12]}-{function_id[12:16]}-{function_id[16:20]}-{function_id[20:]}"
                print(f"🔧 Converted function ID to: {formatted_function_id}")
            
            # Create minimal node data
            nodes_data = [
                {
                    "node_type": "PrintString",
                    "node_id": "test1",
                    "node_position": [0, 0],
                    "node_properties": {}
                }
            ]
            
            nodes_json = json.dumps(nodes_data)
            print(f"📋 Calling with:")
            print(f"   blueprint_path: {blueprint_path}")
            print(f"   function_id: {formatted_function_id}")
            print(f"   nodes_json: {nodes_json}")
            
            print("⏳ Calling add_nodes_bulk...")
            result = node_creator.add_nodes_bulk(blueprint_path, formatted_function_id, nodes_json)
            
            print(f"✅ add_nodes_bulk returned: {result}")
            
            if result:
                try:
                    result_data = json.loads(result)
                    print(f"📊 Parsed result: {result_data}")
                    return True
                except json.JSONDecodeError as e:
                    print(f"⚠️ Result is not valid JSON: {e}")
                    print(f"   Raw result: {result}")
                    return True  # Still consider it a success if we got a response
            else:
                print("❌ add_nodes_bulk returned empty result")
                return False
                
        except Exception as e:
            print(f"❌ add_nodes_bulk call failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # This script should be run from within Unreal Engine's Python console
    print("🚨 This script should be run from within Unreal Engine's Python console!")
    print("🚨 Copy and paste this code into the Python console in Unreal Engine.")
    
    # But we can still try to run it if unreal module is available
    try:
        test_direct_cpp_call()
    except ImportError:
        print("❌ unreal module not available - run this from within Unreal Engine")
