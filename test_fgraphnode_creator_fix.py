#!/usr/bin/env python3

import socket
import json
import time

def test_fgraphnode_creator_fix():
    """Test the FGraphNodeCreator fix for C++ node creation"""
    
    print("🚨 TESTING: FGraphNodeCreator Fix")
    print("🔧 This test uses the FIXED C++ implementation with:")
    print("   - FGraphNodeCreator<UK2Node_CallFunction> pattern")
    print("   - Proper Creator.Finalize() call")
    print("   - Exception handling")
    print("   - Hardcoded PrintString node creation")
    print()
    
    # Connect to Unreal Engine socket server
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(('localhost', 9877))
        print("✅ Connected to Unreal Engine socket server")
    except Exception as e:
        print(f"❌ Failed to connect to socket server: {e}")
        return False
    
    try:
        # Test: Simple node creation with minimal data
        print("\n🧪 TEST: Creating node with FGraphNodeCreator...")
        
        command = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/ThirdPerson/Blueprints/BP_ThirdPersonCharacter.BP_ThirdPersonCharacter",
            "function_id": "EventGraph",  # Use EventGraph instead of GUID
            "nodes": [
                {
                    "id": "test_node",
                    "node_type": "PrintString",
                    "node_position": [100, 100],
                    "node_properties": {}
                }
            ]
        }
        
        # Send command
        command_json = json.dumps(command)
        sock.send(command_json.encode('utf-8'))
        print(f"📤 Sent command: {command_json}")
        
        # Receive response
        response_data = sock.recv(4096)
        response = response_data.decode('utf-8')
        print(f"📥 Received response: {response}")
        
        # Parse response
        try:
            response_obj = json.loads(response)
            if response_obj.get("success"):
                print("✅ TEST PASSED: Node created successfully with FGraphNodeCreator!")
                print("🎯 BREAKTHROUGH: C++ crash is FIXED!")
                return True
            else:
                print(f"❌ TEST FAILED: {response_obj.get('error', 'Unknown error')}")
                return False
        except json.JSONDecodeError:
            print(f"❌ TEST FAILED: Invalid JSON response: {response}")
            return False
            
    except Exception as e:
        print(f"❌ TEST FAILED: Exception occurred: {e}")
        return False
    finally:
        sock.close()

if __name__ == "__main__":
    print("🚀 Starting FGraphNodeCreator Fix Test")
    print("=" * 50)
    
    success = test_fgraphnode_creator_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 BREAKTHROUGH! FGraphNodeCreator fix is working!")
        print("🔥 C++ crash is RESOLVED!")
        print("📈 Ready to implement full node creation system!")
    else:
        print("💥 C++ crash still occurring - need further investigation.")
    
    print("Test completed.")
