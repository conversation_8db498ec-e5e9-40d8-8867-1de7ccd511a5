#!/usr/bin/env python3
"""
Test the simplest possible command - handshake.
"""

import json
import socket
import time

def test_handshake():
    """Test handshake command."""
    
    print("🤝 Testing Handshake Command")
    print("=" * 30)
    
    try:
        # Try IPv4 first since that's what the server binds to
        print("🔌 Connecting via IPv4...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('127.0.0.1', 9877))
        
        handshake_command = {
            "type": "handshake",
            "client": "test_client"
        }
        
        print("📤 Sending handshake...")
        request_json = json.dumps(handshake_command) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        print("📥 Waiting for response...")
        response_data = sock.recv(1024).decode('utf-8')
        sock.close()
        
        print(f"✅ Response received: {response_data}")
        return True
        
    except Exception as e:
        print(f"❌ IPv4 failed: {e}")
    
    try:
        # Try IPv6
        print("🔌 Connecting via IPv6...")
        sock = socket.socket(socket.AF_INET6, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('::1', 9877))
        
        handshake_command = {
            "type": "handshake",
            "client": "test_client"
        }
        
        print("📤 Sending handshake...")
        request_json = json.dumps(handshake_command) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        print("📥 Waiting for response...")
        response_data = sock.recv(1024).decode('utf-8')
        sock.close()
        
        print(f"✅ Response received: {response_data}")
        return True
        
    except Exception as e:
        print(f"❌ IPv6 failed: {e}")
        return False

if __name__ == "__main__":
    test_handshake()
