#!/usr/bin/env python3
"""
Simple test to bypass context getting and test Blueprint function generation directly.
"""

import json
import socket
import time

def test_simple_blueprint():
    """Test Blueprint function generation without context getting."""
    
    print("🧪 Testing Simple Blueprint Function Generation")
    print("=" * 50)
    
    try:
        # Connect to Unreal Engine
        print("🔌 Connecting to Unreal Engine...")
        sock = socket.socket(socket.AF_INET6, socket.SOCK_STREAM)
        sock.settimeout(30)
        sock.connect(('::1', 9877))
        print("✅ Connected!")
        
        # Send a simple test request that bypasses context getting
        test_request = {
            "type": "generate_smart_blueprint_function",
            "function_name": "SimpleTest",
            "description": "Add two numbers",
            "inputs": [{"name": "A", "type": "float"}, {"name": "B", "type": "float"}],
            "outputs": [{"name": "Sum", "type": "float"}],
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "use_current_context": False  # Bypass context getting
        }
        
        print("📤 Sending simple request...")
        request_json = json.dumps(test_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        print("📥 Waiting for response...")
        response_data = sock.recv(8192).decode('utf-8')
        sock.close()
        
        print("✅ Response received!")
        print(f"📊 Response length: {len(response_data)} characters")
        
        # Try to parse as JSON
        try:
            response = json.loads(response_data)
            print(f"📊 Success: {response.get('success', 'Unknown')}")
            if 'connections_made' in response:
                print(f"🔗 Connections made: {response['connections_made']}")
            if 'nodes_created' in response:
                print(f"🔧 Nodes created: {response['nodes_created']}")
            if 'error' in response:
                print(f"❌ Error: {response['error']}")
            return True
        except json.JSONDecodeError:
            print("⚠️ Response is not JSON:")
            print(response_data[:500] + "..." if len(response_data) > 500 else response_data)
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_simple_blueprint()
