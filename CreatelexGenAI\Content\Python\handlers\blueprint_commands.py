import unreal
import json
from typing import Dict, Any, <PERSON>, Tuple, Union, Optional
import time
from difflib import get_close_matches

from utils import unreal_conversions as uc
from utils import logging as log

"""
NODE_PROPERTIES DOCUMENTATION
============================

The `node_properties` parameter allows you to set default values for node pins when creating nodes.
This is essential for configuring nodes with specific values without manual pin connection.

SUPPORTED PROPERTY TYPES:
- String: Text values (e.g., "Hello World", "MyVariable")
- Number: Numeric values (e.g., 42, 3.14, -1.5)
- Boolean: True/false values (e.g., true, false)

USAGE PATTERNS:
1. Pin Name Mapping: Properties are mapped to pins by name
2. Type Conversion: Values are automatically converted to the appropriate pin type
3. Special Node Handling: Some nodes have special property requirements

EXAMPLES:

1. String Literal Node:
   {
       "node_type": "MakeLiteralString",
       "node_properties": {
           "string_value": "Hello World"
       }
   }

2. Random Integer Node:
   {
       "node_type": "RandomIntegerInRange", 
       "node_properties": {
           "min": 1,
           "max": 100
       }
   }

3. Print String Node:
   {
       "node_type": "PrintString",
       "node_properties": {
           "InString": "Debug Message",
           "bPrintToScreen": true,
           "bPrintToLog": true,
           "Duration": 2.0
       }
   }

4. Variable Get/Set Nodes:
   {
       "node_type": "VariableGet",
       "node_properties": {
           "variable_name": "MyVariable"
       }
   }

COMMON PITFALLS:
- Pin names are case-sensitive and must match exactly
- Unknown pin names will be logged as warnings but won't cause errors
- Boolean values should be true/false (not "true"/"false" strings)
- Some nodes require specific properties (e.g., VariableGet needs variable_name)

ERROR HANDLING:
- Invalid JSON in node_properties will be logged as warnings
- Missing required properties for special nodes will cause creation failure
- Pin type mismatches are handled gracefully with conversion attempts

For detailed pin information, use the get_node_pins() function after node creation.
"""

# --- DYNAMIC BLUEPRINT NODE REGISTRY AND SEARCH ---
_blueprint_registry = None

def build_blueprint_node_registry():
    """
    Build a comprehensive registry of ALL available Blueprint nodes.
    This registry should support ANY type of Blueprint function generation.
    """
    registry = []
    log.log_info("[REGISTRY] Building comprehensive Blueprint node registry for ALL function types...")

    # Try enhanced dynamic introspection first
    try:
        all_classes = getattr(unreal, 'all_classes', None)
        if callable(all_classes):
            log.log_info("[REGISTRY] Using enhanced dynamic class introspection")
            class_count = 0
            function_count = 0

            # Get all Blueprint-callable functions from ALL classes
            for uclass in unreal.all_classes():
                class_count += 1
                try:
                    class_name = uclass.get_name()

                    # Skip internal/editor-only classes that aren't useful for Blueprint generation
                    if any(skip in class_name for skip in ['Editor', 'Factory', 'Importer', 'Exporter']):
                        continue

                    for func in uclass.get_functions():
                        function_count += 1

                        # Include ALL Blueprint-callable functions, not just basic ones
                        if (func.has_meta_data("BlueprintCallable") or
                            func.has_meta_data("BlueprintPure") or
                            func.has_meta_data("BlueprintImplementableEvent") or
                            func.has_meta_data("BlueprintNativeEvent")):

                            func_name = func.get_name()

                            # Get function metadata for better categorization
                            category = func.get_meta_data("Category") if func.has_meta_data("Category") else "General"
                            keywords = func.get_meta_data("Keywords") if func.has_meta_data("Keywords") else ""
                            tooltip = func.get_meta_data("ToolTip") if func.has_meta_data("ToolTip") else ""

                            registry.append({
                                "class": class_name,
                                "function_name": func_name,
                                "function_class": class_name,
                                "category": category,
                                "keywords": keywords,
                                "description": tooltip or func.get_doc() if hasattr(func, "get_doc") else "",
                                "is_pure": func.has_meta_data("BlueprintPure"),
                                "is_event": func.has_meta_data("BlueprintImplementableEvent") or func.has_meta_data("BlueprintNativeEvent")
                            })

                except Exception as e:
                    # Skip classes that can't be introspected but continue with others
                    continue

            log.log_info(f"[REGISTRY] Dynamic introspection found {len(registry)} Blueprint functions from {class_count} classes")
            if len(registry) > 100:  # Lower threshold since we want comprehensive coverage
                log.log_info(f"[REGISTRY] Successfully built comprehensive registry with {len(registry)} functions")
                return registry
            else:
                log.log_warning(f"[REGISTRY] Dynamic introspection returned {len(registry)} results, supplementing with static registry")
        else:
            log.log_warning("[REGISTRY] 'unreal.all_classes()' not available, using comprehensive static registry")
    except Exception as e:
        log.log_warning(f"[REGISTRY] Error during dynamic introspection: {e}, using comprehensive static registry")

    # Enhanced static fallback registry with comprehensive node coverage
    log.log_info("[REGISTRY] Using enhanced static registry")
    static_registry = [
        # === MATH LIBRARY ===
        {'class': 'KismetMathLibrary', 'function_name': 'Add_FloatFloat', 'function_class': 'KismetMathLibrary', 'description': 'Addition (float + float)'},
        {'class': 'KismetMathLibrary', 'function_name': 'Add_IntInt', 'function_class': 'KismetMathLibrary', 'description': 'Addition (int + int)'},
        {'class': 'KismetMathLibrary', 'function_name': 'Add_VectorVector', 'function_class': 'KismetMathLibrary', 'description': 'Addition (vector + vector)'},
        {'class': 'KismetMathLibrary', 'function_name': 'Subtract_FloatFloat', 'function_class': 'KismetMathLibrary', 'description': 'Subtraction (float - float)'},
        {'class': 'KismetMathLibrary', 'function_name': 'Subtract_IntInt', 'function_class': 'KismetMathLibrary', 'description': 'Subtraction (int - int)'},
        {'class': 'KismetMathLibrary', 'function_name': 'Multiply_FloatFloat', 'function_class': 'KismetMathLibrary', 'description': 'Multiplication (float * float)'},
        {'class': 'KismetMathLibrary', 'function_name': 'Multiply_IntInt', 'function_class': 'KismetMathLibrary', 'description': 'Multiplication (int * int)'},
        {'class': 'KismetMathLibrary', 'function_name': 'Divide_FloatFloat', 'function_class': 'KismetMathLibrary', 'description': 'Division (float / float)'},
        {'class': 'KismetMathLibrary', 'function_name': 'RandomFloat', 'function_class': 'KismetMathLibrary', 'description': 'Returns a random float between 0 and 1'},
        {'class': 'KismetMathLibrary', 'function_name': 'RandomInteger', 'function_class': 'KismetMathLibrary', 'description': 'Returns a random integer'},
        {'class': 'KismetMathLibrary', 'function_name': 'RandomIntegerInRange', 'function_class': 'KismetMathLibrary', 'description': 'Returns a random integer between Min and Max'},
        {'class': 'KismetMathLibrary', 'function_name': 'RandomFloatInRange', 'function_class': 'KismetMathLibrary', 'description': 'Returns a random float between Min and Max'},
        {'class': 'KismetMathLibrary', 'function_name': 'Sin', 'function_class': 'KismetMathLibrary', 'description': 'Returns the sine of the input'},
        {'class': 'KismetMathLibrary', 'function_name': 'Cos', 'function_class': 'KismetMathLibrary', 'description': 'Returns the cosine of the input'},
        {'class': 'KismetMathLibrary', 'function_name': 'Tan', 'function_class': 'KismetMathLibrary', 'description': 'Returns the tangent of the input'},
        {'class': 'KismetMathLibrary', 'function_name': 'MakeVector', 'function_class': 'KismetMathLibrary', 'description': 'Creates a vector from X, Y, Z components'},
        {'class': 'KismetMathLibrary', 'function_name': 'BreakVector', 'function_class': 'KismetMathLibrary', 'description': 'Breaks a vector into X, Y, Z components'},
        {'class': 'KismetMathLibrary', 'function_name': 'VectorLength', 'function_class': 'KismetMathLibrary', 'description': 'Returns the length of a vector'},
        {'class': 'KismetMathLibrary', 'function_name': 'Normalize', 'function_class': 'KismetMathLibrary', 'description': 'Normalizes a vector'},

        # === SYSTEM LIBRARY ===
        {'class': 'KismetSystemLibrary', 'function_name': 'PrintString', 'function_class': 'KismetSystemLibrary', 'description': 'Prints a string to the screen and log'},
        {'class': 'KismetSystemLibrary', 'function_name': 'Delay', 'function_class': 'KismetSystemLibrary', 'description': 'Delays execution for a specified duration'},
        {'class': 'KismetSystemLibrary', 'function_name': 'GetTimeSeconds', 'function_class': 'KismetSystemLibrary', 'description': 'Returns the current time in seconds'},

        # === STRING LIBRARY ===
        {'class': 'KismetStringLibrary', 'function_name': 'Append', 'function_class': 'KismetStringLibrary', 'description': 'Concatenates two strings'},
        {'class': 'KismetStringLibrary', 'function_name': 'StringLength', 'function_class': 'KismetStringLibrary', 'description': 'Returns the length of a string'},
        {'class': 'KismetStringLibrary', 'function_name': 'GetCharacterAtIndex', 'function_class': 'KismetStringLibrary', 'description': 'Gets the character at a specific index in a string'},
        {'class': 'KismetStringLibrary', 'function_name': 'MakeLiteralString', 'function_class': 'KismetStringLibrary', 'description': 'Creates a literal string value'},

        # === CONTROL FLOW ===
        {'class': 'BlueprintGraph', 'function_name': 'Branch', 'function_class': 'BlueprintGraph', 'description': 'Conditional execution branch'},
        {'class': 'BlueprintGraph', 'function_name': 'ForLoop', 'function_class': 'BlueprintGraph', 'description': 'For loop iteration'},
        {'class': 'BlueprintGraph', 'function_name': 'WhileLoop', 'function_class': 'BlueprintGraph', 'description': 'While loop iteration'},
        {'class': 'BlueprintGraph', 'function_name': 'Sequence', 'function_class': 'BlueprintGraph', 'description': 'Sequential execution of multiple outputs'},

        # === ACTOR FUNCTIONS ===
        {'class': 'Actor', 'function_name': 'GetActorLocation', 'function_class': 'Actor', 'description': 'Gets the location of an actor'},
        {'class': 'Actor', 'function_name': 'SetActorLocation', 'function_class': 'Actor', 'description': 'Sets the location of an actor'},
        {'class': 'Actor', 'function_name': 'GetActorRotation', 'function_class': 'Actor', 'description': 'Gets the rotation of an actor'},
        {'class': 'Actor', 'function_name': 'SetActorRotation', 'function_class': 'Actor', 'description': 'Sets the rotation of an actor'},
    ]

    log.log_info(f"[REGISTRY] Static registry contains {len(static_registry)} Blueprint functions")
    return static_registry

def get_blueprint_registry():
    global _blueprint_registry
    if _blueprint_registry is None:
        _blueprint_registry = build_blueprint_node_registry()
    return _blueprint_registry

def search_blueprint_nodes(query, top_n=3):
    registry = get_blueprint_registry()
    search_strings = [
        f"{entry['function_name']} {entry['function_class']} {entry['description']}"
        for entry in registry
    ]
    matches = get_close_matches(query.lower(), [s.lower() for s in search_strings], n=top_n, cutoff=0.3)
    results = []
    for match in matches:
        for entry in registry:
            if match in f"{entry['function_name']} {entry['function_class']} {entry['description']}":
                results.append(entry)
                break
    return results

# --- MCP COMMAND: SEARCH BLUEPRINT NODES ---
def handle_search_blueprint_nodes(command):
    """
    MCP command to search Blueprint nodes/functions by query string.
    Args:
        command: { 'query': <string>, 'top_n': <int> }
    Returns:
        { 'success': True, 'results': [ ... ] }
    """
    query = command.get('query', '')
    top_n = command.get('top_n', 3)
    results = search_blueprint_nodes(query, top_n=top_n)
    return { 'success': True, 'results': results }

# --- DYNAMIC NODE LOOKUP FOR FUNCTION GENERATION ---
def find_blueprint_node_dynamic(query):
    results = search_blueprint_nodes(query, top_n=1)
    if results and results[0].get('function_name'):
        entry = results[0]
        return {
            'node_type': 'K2Node_CallFunction',
            'function_name': entry['function_name'],
            'function_class': entry['function_class'],
        }
    log.log_warning(f"[AI Plugin] No matching Blueprint node found for: '{query}'. Node creation skipped.")
    return {}

def handle_create_blueprint(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to create a new Blueprint from a specified parent class
    
    Args:
        command: The command dictionary containing:
            - blueprint_name: Name for the new Blueprint
            - parent_class: Parent class name or path (e.g., "Actor", "/Script/Engine.Actor")
            - save_path: Path to save the Blueprint asset (e.g., "/Game/Blueprints")
            
    Returns:
        Response dictionary with success/failure status and the Blueprint path if successful
    """
    try:
        blueprint_name = command.get("blueprint_name", "NewBlueprint")
        parent_class = command.get("parent_class", "Actor")
        save_path = command.get("save_path", "/Game/Blueprints")

        log.log_command("create_blueprint", f"Name: {blueprint_name}, Parent: {parent_class}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        blueprint = gen_bp_utils.create_blueprint(blueprint_name, parent_class, save_path)

        if blueprint:
            blueprint_path = f"{save_path}/{blueprint_name}"
            log.log_result("create_blueprint", True, f"Path: {blueprint_path}")
            return {"success": True, "blueprint_path": blueprint_path}
        else:
            log.log_error(f"Failed to create Blueprint {blueprint_name}")
            return {"success": False, "error": f"Failed to create Blueprint {blueprint_name}"}

    except Exception as e:
        log.log_error(f"Error creating blueprint: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_add_component(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to add a component to a Blueprint
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - component_class: Component class to add (e.g., "StaticMeshComponent")
            - component_name: Name for the new component
            
    Returns:
        Response dictionary with success/failure status
    """
    try:
        blueprint_path = command.get("blueprint_path")
        component_class = command.get("component_class")
        component_name = command.get("component_name")

        if not blueprint_path or not component_class:
            log.log_error("Missing required parameters for add_component")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("add_component", f"Blueprint: {blueprint_path}, Component: {component_class}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        success = gen_bp_utils.add_component(blueprint_path, component_class, component_name or "")

        if success:
            log.log_result("add_component", True, f"Added {component_class} to {blueprint_path}")
            return {"success": True}
        else:
            log.log_error(f"Failed to add component {component_class} to {blueprint_path}")
            return {"success": False, "error": f"Failed to add component {component_class} to {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error adding component: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_add_variable(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to add a variable to a Blueprint
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - variable_name: Name for the new variable
            - variable_type: Type of the variable (e.g., "float", "vector", "boolean")
            - default_value: Default value for the variable (optional)
            - category: Category for organizing variables in the Blueprint editor (optional)
            
    Returns:
        Response dictionary with success/failure status
    """
    try:
        blueprint_path = command.get("blueprint_path")
        variable_name = command.get("variable_name")
        variable_type = command.get("variable_type")
        default_value = command.get("default_value", "")
        category = command.get("category", "Default")

        if not blueprint_path or not variable_name or not variable_type:
            log.log_error("Missing required parameters for add_variable")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("add_variable",
                        f"Blueprint: {blueprint_path}, Variable: {variable_name}, Type: {variable_type}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        success = gen_bp_utils.add_variable(blueprint_path, variable_name, variable_type, str(default_value), category)

        if success:
            log.log_result("add_variable", True, f"Added {variable_type} variable {variable_name} to {blueprint_path}")
            return {"success": True}
        else:
            log.log_error(f"Failed to add variable {variable_name} to {blueprint_path}")
            return {"success": False, "error": f"Failed to add variable {variable_name} to {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error adding variable: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_add_function(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to add a function to a Blueprint
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_name: Name for the new function
            - inputs: List of input parameters [{"name": "param1", "type": "float"}, ...] (optional)
            - outputs: List of output parameters (optional)
            
    Returns:
        Response dictionary with success/failure status and the function ID if successful
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_name = command.get("function_name")
        inputs = command.get("inputs", [])
        outputs = command.get("outputs", [])

        # Always serialize inputs/outputs to JSON strings
        if not isinstance(inputs, str):
            inputs_json = json.dumps(inputs)
        else:
            inputs_json = inputs
        if not isinstance(outputs, str):
            outputs_json = json.dumps(outputs)
        else:
            outputs_json = outputs

        if not blueprint_path or not function_name:
            log.log_error("Missing required parameters for add_function")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("add_function", f"Blueprint: {blueprint_path}, Function: {function_name}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        function_id = gen_bp_utils.add_function(blueprint_path, function_name, inputs_json, outputs_json)

        if function_id:
            log.log_result("add_function", True,
                           f"Added function {function_name} to {blueprint_path} with ID: {function_id}")
            return {"success": True, "function_id": function_id}
        else:
            log.log_error(f"Failed to add function {function_name} to {blueprint_path}")
            return {"success": False, "error": f"Failed to add function {function_name} to {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error adding function: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_add_node(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to add any type of node to a Blueprint graph
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function to add the node to
            - node_type: Type of node to add
            - node_position: Position of the node in the graph [X, Y]
            - node_properties: Dictionary of properties to set on the node (optional)
                * Maps property names to pin names for setting default values
                * Supports string, number, and boolean values
                * Special handling for VariableGet/Set nodes (use "variable_name")
                * See module docstring for detailed examples and usage patterns
            - target_class: Optional class to use for function calls (default: "Actor")
    Returns:
        Response dictionary with success/failure status, the node ID if successful, and pin metadata
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        node_type = command.get("node_type")
        node_position = command.get("node_position", [0, 0])
        node_properties = command.get("node_properties", {})

        if not blueprint_path or not function_id or not node_type:
            log.log_error("Missing required parameters for add_node")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("add_node", f"Blueprint: {blueprint_path}, Node: {node_type}")

        # Convert node properties to JSON for C++ function
        node_properties_json = json.dumps(node_properties)

        # Call the C++ implementation from UGenBlueprintNodeCreator
        node_creator = unreal.GenBlueprintNodeCreator
        node_id = node_creator.add_node(blueprint_path, function_id, node_type,
                                        node_position[0], node_position[1],
                                        node_properties_json)

        if node_id:
            # --- NEW: Fetch pin metadata for the created node ---
            pin_metadata = get_node_pins(blueprint_path, function_id, node_id)
            log.log_result("add_node", True, f"Added node {node_type} to {blueprint_path} with ID: {node_id}")
            return {"success": True, "node_id": node_id, "pin_metadata": pin_metadata}
        else:
            log.log_error(f"Failed to add node {node_type} to {blueprint_path}")
            return {"success": False, "error": f"Failed to add node {node_type} to {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error adding node: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

# Helper to fetch pin metadata for a node (stub, to be implemented)
def get_node_pins(blueprint_path, function_id, node_id):
    """
    Fetch input/output pin names/types for a node using the C++ bridge.
    Returns: { "inputs": [...], "outputs": [...] }
    """
    try:
        node_creator = unreal.GenBlueprintNodeCreator
        pins_json = node_creator.get_node_pins(blueprint_path, function_id, node_id)
        import json
        pin_metadata = json.loads(pins_json)
        # Optionally filter/validate structure here
        return pin_metadata
    except Exception as e:
        log.log_error(f"Error fetching pin metadata: {str(e)}")
        return {"inputs": [], "outputs": [], "error": str(e)}


def handle_connect_nodes(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to connect two Blueprint nodes using improved Python API
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function containing the nodes
            - source_node_id: GUID of the source node
            - source_pin: Name of the output pin on the source node
            - target_node_id: GUID of the target node
            - target_pin: Name of the input pin on the target node
            
    Returns:
        Response dictionary with success/failure status and connection details
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        source_node_id = command.get("source_node_id")
        source_pin = command.get("source_pin", "exec")  # Default to exec pin
        target_node_id = command.get("target_node_id")
        target_pin = command.get("target_pin", "exec")  # Default to exec pin

        if not all([blueprint_path, function_id, source_node_id, target_node_id]):
            log.log_error("Missing required parameters for connect_nodes")
            return {"success": False, "error": "Missing required parameters: blueprint_path, function_id, source_node_id, target_node_id"}

        log.log_command("connect_nodes",
                        f"Blueprint: {blueprint_path}, {source_node_id}.{source_pin} -> {target_node_id}.{target_pin}")

        # Try using the improved connection function first
        try:
            from improved_blueprint_connections import connect_blueprint_nodes_improved
            
            result = connect_blueprint_nodes_improved(
                blueprint_path=blueprint_path,
                function_name=function_id,  # Use function_id as function_name for compatibility
                source_node_guid=source_node_id,
                source_pin=source_pin,
                target_node_guid=target_node_id,
                target_pin=target_pin
            )
            
            if result.get("success"):
                log.log_result("connect_nodes", True, f"Connected nodes in {blueprint_path}")
                return result
            else:
                log.log_warning(f"Improved connection failed: {result.get('error')}")
                
        except ImportError as e:
            log.log_warning(f"Could not import improved_blueprint_connections: {e}")
        except Exception as e:
            log.log_warning(f"Error with improved Blueprint connection: {e}")
            
        # Fallback to original method if improved version fails
        log.log_info("Falling back to original Blueprint connection method")
        
        gen_bp_utils = unreal.GenBlueprintUtils
        result_json = gen_bp_utils.connect_nodes(blueprint_path, function_id,
                                                 source_node_id, source_pin,
                                                 target_node_id, target_pin)
        result = json.loads(result_json)

        if result.get("success"):
            log.log_result("connect_nodes", True, f"Connected nodes in {blueprint_path} (fallback method)")
            return {"success": True}
        else:
            log.log_error(f"Failed to connect nodes: {result.get('error')}")
            return result  # Pass through the detailed response with available pins

    except Exception as e:
        log.log_error(f"Error connecting nodes: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_compile_blueprint(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to compile a Blueprint
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            
    Returns:
        Response dictionary with success/failure status
    """
    try:
        blueprint_path = command.get("blueprint_path")

        if not blueprint_path:
            log.log_error("Missing required parameters for compile_blueprint")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("compile_blueprint", f"Blueprint: {blueprint_path}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        success = gen_bp_utils.compile_blueprint(blueprint_path)

        if success:
            log.log_result("compile_blueprint", True, f"Compiled blueprint: {blueprint_path}")
            return {"success": True}
        else:
            log.log_error(f"Failed to compile blueprint: {blueprint_path}")
            return {"success": False, "error": f"Failed to compile blueprint: {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error compiling blueprint: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_spawn_blueprint(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to spawn a Blueprint actor in the level
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - location: [X, Y, Z] coordinates (optional)
            - rotation: [Pitch, Yaw, Roll] in degrees (optional)
            - scale: [X, Y, Z] scale factors (optional)
            - actor_label: Optional custom name for the actor
            
    Returns:
        Response dictionary with success/failure status and the actor name if successful
    """
    try:
        blueprint_path = command.get("blueprint_path")
        location = command.get("location", (0, 0, 0))
        rotation = command.get("rotation", (0, 0, 0))
        scale = command.get("scale", (1, 1, 1))
        actor_label = command.get("actor_label", "")

        if not blueprint_path:
            log.log_error("Missing required parameters for spawn_blueprint")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("spawn_blueprint", f"Blueprint: {blueprint_path}, Label: {actor_label}")

        # Convert to Unreal types
        loc = uc.to_unreal_vector(location)
        rot = uc.to_unreal_rotator(rotation)
        scale_vec = uc.to_unreal_vector(scale)

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        actor = gen_bp_utils.spawn_blueprint(blueprint_path, loc, rot, scale_vec, actor_label)

        if actor:
            actor_name = actor.get_actor_label()
            log.log_result("spawn_blueprint", True, f"Spawned blueprint: {blueprint_path} as {actor_name}")
            return {"success": True, "actor_name": actor_name}
        else:
            log.log_error(f"Failed to spawn blueprint: {blueprint_path}")
            return {"success": False, "error": f"Failed to spawn blueprint: {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error spawning blueprint: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_add_nodes_bulk(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to add multiple nodes to a Blueprint graph in a single operation
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function to add the nodes to
            - nodes: Array of node definitions, each containing:
                * id: Optional ID for referencing the node (string)
                * node_type: Type of node to add (string)
                * node_position: Position of the node in the graph [X, Y]
                * node_properties: Properties to set on the node (optional)
            
    Returns:
        Response dictionary with success/failure status and node IDs mapped to reference IDs
    """

    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        nodes = command.get("nodes", [])

        if not blueprint_path or not function_id or not nodes:
            log.log_error("Missing required parameters for add_nodes_bulk")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("add_nodes_bulk", f"Blueprint: {blueprint_path}, Adding {len(nodes)} nodes")

        # Prepare nodes in the format expected by the C++ function
        # Ensure each node has a ref_id that matches its node_id for proper mapping
        prepared_nodes = []
        for node in nodes:
            prepared_node = node.copy()
            if "node_id" in prepared_node and "ref_id" not in prepared_node:
                prepared_node["ref_id"] = prepared_node["node_id"]
            prepared_nodes.append(prepared_node)

        nodes_json = json.dumps(prepared_nodes)

        # Call the C++ implementation from UGenBlueprintNodeCreator
        node_creator = unreal.GenBlueprintNodeCreator
        results_json = node_creator.add_nodes_bulk(blueprint_path, function_id, nodes_json)
        
        if results_json:
            results = json.loads(results_json)
            node_mapping = {}

            # Create a mapping from reference IDs to actual node GUIDs
            for node_result in results:
                if "ref_id" in node_result:
                    node_mapping[node_result["ref_id"]] = node_result["node_guid"]
                else:
                    # For nodes without a reference ID, just include the GUID
                    node_mapping[f"node_{len(node_mapping)}"] = node_result["node_guid"]

            log.log_result("add_nodes_bulk", True, f"Added {len(results)} nodes to {blueprint_path}")
            return {"success": True, "nodes": node_mapping}
        else:
            log.log_error(f"Failed to add nodes to {blueprint_path}")
            return {"success": False, "error": f"Failed to add nodes to {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error adding nodes: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_connect_nodes_bulk(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to connect multiple pairs of nodes in a Blueprint graph using improved Python API
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function containing the nodes
            - connections: Array of connection definitions, each containing:
                * source_node_id: ID of the source node
                * source_pin: Name of the source pin (optional, defaults to "exec")
                * target_node_id: ID of the target node
                * target_pin: Name of the target pin (optional, defaults to "exec")
            
    Returns:
        Response dictionary with detailed connection results
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        connections = command.get("connections", [])

        if not blueprint_path or not function_id or not connections:
            log.log_error("Missing required parameters for connect_nodes_bulk")
            return {"success": False, "error": "Missing required parameters: blueprint_path, function_id, connections"}

        log.log_command("connect_nodes_bulk", f"Blueprint: {blueprint_path}, Making {len(connections)} connections")

        # Try using the improved bulk connection function first
        try:
            from improved_blueprint_connections import connect_blueprint_nodes_bulk_improved
            
            # Prepare connections with defaults for pins if not specified
            prepared_connections = []
            for conn in connections:
                prepared_conn = {
                    "source_node_guid": conn.get("source_node_id"),
                    "source_pin": conn.get("source_pin", "exec"),
                    "target_node_guid": conn.get("target_node_id"), 
                    "target_pin": conn.get("target_pin", "exec")
                }
                prepared_connections.append(prepared_conn)
            
            result = connect_blueprint_nodes_bulk_improved(
                blueprint_path=blueprint_path,
                function_name=function_id,  # Use function_id as function_name for compatibility
                connections=prepared_connections
            )
            
            if result.get("success") or result.get("successful_connections", 0) > 0:
                log.log_result("connect_nodes_bulk", result.get("success", False),
                              f"Connected {result.get('successful_connections', 0)}/{result.get('total_connections', 0)} node pairs")
                return result
            else:
                log.log_warning(f"Improved bulk connection failed: {result.get('error')}")
                
        except ImportError as e:
            log.log_warning(f"Could not import improved_blueprint_connections: {e}")
        except Exception as e:
            log.log_warning(f"Error with improved bulk Blueprint connection: {e}")
            
        # Fallback to original method if improved version fails
        log.log_info("Falling back to original bulk connection method")

        # Convert connections list to JSON for C++ function
        connections_json = json.dumps(connections)

        # Call the C++ implementation - now returns a JSON string instead of boolean
        gen_bp_utils = unreal.GenBlueprintUtils
        result_json = gen_bp_utils.connect_nodes_bulk(blueprint_path, function_id, connections_json)

        # Parse the JSON result
        try:
            result_data = json.loads(result_json)
            log.log_result("connect_nodes_bulk", result_data.get("success", False),
                           f"Connected {result_data.get('successful_connections', 0)}/{result_data.get('total_connections', 0)} node pairs in {blueprint_path}")

            # Return the full result data for detailed error reporting
            return result_data
        except json.JSONDecodeError:
            log.log_error(f"Failed to parse JSON result from connect_nodes_bulk: {result_json}")
            return {"success": False, "error": "Failed to parse connection results"}

    except Exception as e:
        log.log_error(f"Error connecting nodes: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_delete_node(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to delete a node from a Blueprint graph
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function containing the node
            - node_id: ID of the node to delete
                
    Returns:
        Response dictionary with success/failure status
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        node_id = command.get("node_id")

        if not blueprint_path or not function_id or not node_id:
            log.log_error("Missing required parameters for delete_node")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("delete_node", f"Blueprint: {blueprint_path}, Node ID: {node_id}")

        # Call the C++ implementation from UGenBlueprintNodeCreator
        node_creator = unreal.GenBlueprintNodeCreator
        success = node_creator.delete_node(blueprint_path, function_id, node_id)

        if success:
            log.log_result("delete_node", True, f"Deleted node {node_id} from {blueprint_path}")
            return {"success": True}
        else:
            log.log_error(f"Failed to delete node {node_id} from {blueprint_path}")
            return {"success": False, "error": f"Failed to delete node {node_id}"}

    except Exception as e:
        log.log_error(f"Error deleting node: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_get_all_nodes(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to get all nodes in a Blueprint graph
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function to get nodes from
                
    Returns:
        Response dictionary with success/failure status and a list of nodes with their details
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")

        if not blueprint_path or not function_id:
            log.log_error("Missing required parameters for get_all_nodes")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("get_all_nodes", f"Blueprint: {blueprint_path}, Function ID: {function_id}")

        # Call the C++ implementation from UGenBlueprintNodeCreator
        node_creator = unreal.GenBlueprintNodeCreator
        nodes_json = node_creator.get_all_nodes_in_graph(blueprint_path, function_id)

        if nodes_json:
            # Parse the JSON response
            try:
                nodes = json.loads(nodes_json)
                log.log_result("get_all_nodes", True, f"Retrieved {len(nodes)} nodes from {blueprint_path}")
                return {"success": True, "nodes": nodes}
            except json.JSONDecodeError as e:
                log.log_error(f"Error parsing nodes JSON: {str(e)}")
                return {"success": False, "error": f"Error parsing nodes JSON: {str(e)}"}
        else:
            log.log_error(f"Failed to get nodes from {blueprint_path}")
            return {"success": False, "error": "Failed to get nodes"}

    except Exception as e:
        log.log_error(f"Error getting nodes: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_get_node_suggestions(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to get suggestions for a node type in Unreal Blueprints
    
    Args:
        command: The command dictionary containing:
            - node_type: The partial or full node type to get suggestions for (e.g., "Add", "FloatToDouble")
                
    Returns:
        Response dictionary with success/failure status and a list of suggested node types
    """
    try:
        node_type = command.get("node_type")

        if not node_type:
            log.log_error("Missing required parameter 'node_type' for get_node_suggestions")
            return {"success": False, "error": "Missing required parameter 'node_type'"}

        log.log_command("get_node_suggestions", f"Node Type: {node_type}")

        # Call the C++ implementation from UGenBlueprintNodeCreator
        node_creator = unreal.GenBlueprintNodeCreator
        suggestions_result = node_creator.get_node_suggestions(node_type)

        if suggestions_result:
            if suggestions_result.startswith("SUGGESTIONS:"):
                suggestions = suggestions_result[len("SUGGESTIONS:"):].split(", ")
                log.log_result("get_node_suggestions", True, f"Retrieved {len(suggestions)} suggestions for {node_type}")
                return {"success": True, "suggestions": suggestions}
            else:
                log.log_error(f"Unexpected response format from get_node_suggestions: {suggestions_result}")
                return {"success": False, "error": "Unexpected response format from Unreal"}
        else:
            log.log_result("get_node_suggestions", False, f"No suggestions found for {node_type}")
            return {"success": True, "suggestions": []}  # Empty list for no matches

    except Exception as e:
        log.log_error(f"Error getting node suggestions: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_get_node_guid(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to retrieve the GUID of a pre-existing node in a Blueprint graph.
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - graph_type: "EventGraph" or "FunctionGraph"
            - node_name: Name of the node (e.g., "BeginPlay") for EventGraph
            - function_id: ID of the function for FunctionGraph to get FunctionEntry
            
    Returns:
        Response dictionary with the node's GUID or an error
    """
    try:
        blueprint_path = command.get("blueprint_path")
        graph_type = command.get("graph_type", "EventGraph")
        node_name = command.get("node_name", "")
        function_id = command.get("function_id", "")

        if not blueprint_path:
            log.log_error("Missing blueprint_path for get_node_guid")
            return {"success": False, "error": "Missing blueprint_path"}

        if graph_type not in ["EventGraph", "FunctionGraph"]:
            log.log_error(f"Invalid graph_type: {graph_type}")
            return {"success": False, "error": f"Invalid graph_type: {graph_type}"}

        log.log_command("get_node_guid", f"Blueprint: {blueprint_path}, Graph: {graph_type}, Node: {node_name or function_id}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        node_guid = gen_bp_utils.get_node_guid(blueprint_path, graph_type, node_name, function_id)

        if node_guid:
            log.log_result("get_node_guid", True, f"Found node GUID: {node_guid}")
            return {"success": True, "node_guid": node_guid}
        else:
            log.log_error(f"Failed to find node: {node_name or 'FunctionEntry'}")
            return {"success": False, "error": f"Node not found: {node_name or 'FunctionEntry'}"}

    except Exception as e:
        log.log_error(f"Error getting node GUID: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_generate_smart_blueprint_function(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to generate a smart Blueprint function with AI-driven node creation - LOCAL IMPLEMENTATION
    Uses direct Unreal Engine Python API calls, similar to create_material pattern
    
    Args:
        command: The command dictionary containing:
            - function_name: Name for the new function
            - description: Description of what the function should do
            - inputs: List of input parameters [{"name": "param1", "type": "int32"}, ...]
            - outputs: List of output parameters [{"name": "result", "type": "string"}, ...]
            - complexity: Function complexity ("simple", "medium", "complex")
            - use_current_context: Whether to use current Blueprint context (optional)
            - blueprint_path: Specific Blueprint path (optional)
            
    Returns:
        Response dictionary with success/failure status and generation details
    """
    try:
        function_name = command.get("function_name", "AIGeneratedFunction")
        description = command.get("description", "")
        inputs = command.get("inputs", [])
        outputs = command.get("outputs", [])
        complexity = command.get("complexity", "medium")
        
        use_current_context = command.get("use_current_context", True)
        blueprint_path = command.get("blueprint_path", "")

        log.log_command("generate_smart_blueprint_function", f"Function: {function_name}, Description: {description}")

        if not function_name or not function_name.strip():
            return {"success": False, "error": "Function name cannot be empty"}
        if not description or not description.strip():
            return {"success": False, "error": "Function description cannot be empty"}

        # STEP 1: Get current Unreal Engine context using C++ sync system
        log.log_info("🔄 Step 1: Getting current Unreal Engine context...")
        context_result = handle_get_unreal_context({})
        context_asset_path = None
        context_asset_type = None
        context_asset_name = None
        
        # Log the full context for debugging
        log.log_info(f"[DEBUG] Full context_result: {json.dumps(context_result, indent=2, default=str)}")

        if context_result.get("success", False):
            log.log_info("✅ Successfully retrieved UE context")
            context_data = context_result.get("context", {})
            blueprint_context = context_data.get("blueprint_context", {})
            editor_context = context_data.get("editor_context", {})

            # Prefer the asset that was open in the window where Sync with AI was clicked
            # Look for 'current_blueprint' or the first open asset
            current_blueprint = blueprint_context.get("current_blueprint", {})
            if current_blueprint and current_blueprint.get("path"):
                context_asset_path = current_blueprint["path"]
                context_asset_type = current_blueprint.get("type", "Blueprint")
                context_asset_name = current_blueprint.get("name", "")
                log.log_info(f"🎯 Using asset from Sync with AI window: {context_asset_name} ({context_asset_type}) at {context_asset_path}")
            else:
                # Try to get from open assets (e.g., Widget, other asset types)
                open_assets = editor_context.get("open_assets", [])
                if open_assets:
                    context_asset = open_assets[0]
                    context_asset_path = context_asset.get("path")
                    context_asset_type = context_asset.get("type", "Unknown")
                    context_asset_name = context_asset.get("name", "")
                    log.log_info(f"🎯 Using first open asset: {context_asset_name} ({context_asset_type}) at {context_asset_path}")

            # --- Robust fallback: search for any valid asset path in the context dict ---
            if not context_asset_path:
                def find_asset_path(d):
                    if isinstance(d, dict):
                        for k, v in d.items():
                            if isinstance(v, str) and "/" in v and (".umap" in v or ".Lvl_" in v or ".Blueprint" in v or ":PersistentLevel" in v):
                                return v
                            elif isinstance(v, dict):
                                found = find_asset_path(v)
                                if found:
                                    return found
                            elif isinstance(v, list):
                                for item in v:
                                    found = find_asset_path(item)
                                    if found:
                                        return found
                    return None
                context_asset_path = find_asset_path(context_result)
                if context_asset_path:
                    log.log_info(f"[DEBUG] Fallback: Found asset path in context: {context_asset_path}")

        # Always use the asset from context if available
        if context_asset_path:
            blueprint_path = context_asset_path
            log.log_info(f"✅ Targeting asset from Sync with AI: {context_asset_name} ({context_asset_type}) at {blueprint_path}")
        else:
            log.log_warning("⚠️ No asset found from Sync with AI context. Falling back to default.")
            blueprint_path = "/Game/Blueprints/BP_Default"
            log.log_info(f"Using fallback blueprint path: {blueprint_path}")

        log.log_info(f"Target Blueprint path: {blueprint_path}")

        # STEP 2: Create the function with proper error handling
        try:
            add_function_result = handle_add_function({
                "blueprint_path": blueprint_path,
                "function_name": function_name,
                "inputs": inputs,
                "outputs": outputs
            })

            if not add_function_result.get("success", False):
                log.log_error(f"Failed to create function: {add_function_result}")
                return {
                    "success": False,
                    "error": f"Failed to create Blueprint function: {add_function_result.get('error', 'Unknown error')}",
                    "blueprint_path": blueprint_path
                }

            function_id = add_function_result.get("function_id")
            log.log_info(f"✅ Function created with ID: {function_id}")
            
        except Exception as e:
            log.log_error(f"Exception during function creation: {str(e)}")
            return {
                "success": False,
                "error": f"Exception creating function: {str(e)}",
                "blueprint_path": blueprint_path
            }

        # STEP 3: Generate nodes based on description and complexity with validation
        try:
            nodes_to_add = _generate_nodes_for_function(function_name, description, inputs, outputs, complexity)
            log.log_info(f"Generated {len(nodes_to_add)} nodes to add: {[node.get('node_type', 'Unknown') for node in nodes_to_add]}")

            if not nodes_to_add:
                log.log_info("No nodes to add - creating simple function")
                return {
                    "success": True,
                    "message": f"Function '{function_name}' created successfully (empty)",
                    "function_name": function_name,
                    "function_id": function_id,
                    "nodes_created": 0,
                    "connections_made": 0,
                    "blueprint_path": blueprint_path
                }
        except Exception as e:
            log.log_error(f"Exception during node generation: {str(e)}")
            return {
                "success": True,
                "message": f"Function '{function_name}' created (node generation failed)",
                "function_name": function_name,
                "function_id": function_id,
                "nodes_created": 0,
                "connections_made": 0,
                "blueprint_path": blueprint_path,
                "warning": f"Node generation failed: {str(e)}"
            }

        # STEP 4: Add nodes to the function with error handling
        nodes_created = 0
        node_mapping = {}
        try:
            add_nodes_result = handle_add_nodes_bulk({
                "blueprint_path": blueprint_path,
                "function_id": function_id,
                "nodes": nodes_to_add
            })
            if add_nodes_result.get("success", False):
                node_mapping = add_nodes_result.get("nodes", {})
                nodes_created = len(node_mapping)
                log.log_info(f"✅ Nodes added: {list(node_mapping.keys())}")
            else:
                log.log_warning(f"⚠️ Node creation failed: {add_nodes_result}")
        except Exception as e:
            log.log_error(f"Exception during node creation: {str(e)}")

        # STEP 5: Connect the nodes with error handling
        connections_made = 0
        if nodes_created > 1 and node_mapping:
            try:
                connections = _generate_connections_for_nodes(nodes_to_add, node_mapping, inputs, outputs)
                if connections:
                    # Convert connection format from internal format to handle_connect_nodes_bulk format
                    formatted_connections = []
                    for conn in connections:
                        formatted_conn = {
                            "source_node_id": conn.get("from_node"),
                            "source_pin": conn.get("from_pin", "exec"),
                            "target_node_id": conn.get("to_node"),
                            "target_pin": conn.get("to_pin", "exec")
                        }
                        formatted_connections.append(formatted_conn)

                    connect_result = handle_connect_nodes_bulk({
                        "blueprint_path": blueprint_path,
                        "function_id": function_id,
                        "connections": formatted_connections
                    })
                    if connect_result.get("success", False):
                        connections_made = connect_result.get("successful_connections", len(connections))
                        log.log_info(f"✅ Nodes connected successfully: {connections_made}")
                    else:
                        log.log_warning(f"⚠️ Node connections failed: {connect_result}")
            except Exception as e:
                log.log_error(f"Exception during node connections: {str(e)}")

        # STEP 6: Compile the Blueprint with error handling
        compiled = False
        try:
            compile_result = handle_compile_blueprint({"blueprint_path": blueprint_path})
            compiled = compile_result.get("success", False)
            if compiled:
                log.log_info(f"✅ Blueprint compiled successfully")
            else:
                log.log_warning(f"⚠️ Blueprint compilation failed: {compile_result}")
        except Exception as e:
            log.log_error(f"Exception during Blueprint compilation: {str(e)}")

        log.log_result("generate_smart_blueprint_function", True, 
                      f"Function: {function_name}, Nodes: {nodes_created}, Connections: {connections_made}")
        return {
            "success": True,
            "message": f"Smart Blueprint function '{function_name}' generated successfully!",
            "function_name": function_name,
            "function_id": function_id,
            "description": description,
            "complexity": complexity,
            "nodes_created": nodes_created,
            "connections_made": connections_made,
            "compiled": compiled,
            "blueprint_path": blueprint_path,
            "generation_summary": f"Created {nodes_created} nodes with {connections_made} connections",
            "context_used": context_result.get("success", False)
        }
    except Exception as e:
        log.log_error(f"Error generating smart Blueprint function: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_generate_blueprint_function(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to generate a basic Blueprint function - LOCAL IMPLEMENTATION
    Simplified version that creates basic function structure
    
    Args:
        command: The command dictionary containing:
            - function_name: Name for the new function
            - description: Description of what the function should do
            - inputs: List of input parameters (optional)
            - outputs: List of output parameters (optional)
            - use_current_context: Whether to use current Blueprint context (optional)
            - blueprint_path: Specific Blueprint path (optional)
            
    Returns:
        Response dictionary with success/failure status
    """
    try:
        # Forward to smart generation with medium complexity
        enhanced_command = command.copy()
        enhanced_command["complexity"] = "medium"
        
        log.log_command("generate_blueprint_function", f"Function: {command.get('function_name', 'Unknown')}")
        
        result = handle_generate_smart_blueprint_function(enhanced_command)
        
        if result.get("success"):
            log.log_result("generate_blueprint_function", True, f"Generated via smart function")
        else:
            log.log_error(f"Basic function generation failed: {result.get('error')}")
            
        return result

    except Exception as e:
        log.log_error(f"Error generating Blueprint function: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_test_automatic_node_creation(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Test function to verify AI-driven automatic node creation is working properly.
    Tests various types of functions to ensure the AI can generate appropriate nodes.
    """
    try:
        log.log_command("test_automatic_node_creation", "Testing AI-driven node creation system")

        # Test multiple function types to verify AI intelligence
        test_cases = [
            {
                "function_name": "TestMathFunction",
                "description": "Calculate the sum of two numbers and multiply by a random factor",
                "inputs": [{"name": "A", "type": "float"}, {"name": "B", "type": "float"}],
                "outputs": [{"name": "Result", "type": "float"}],
                "complexity": "medium",
                "expected_nodes": ["Add_FloatFloat", "Multiply_FloatFloat", "RandomFloat", "PrintString"]
            },
            {
                "function_name": "TestMovementFunction",
                "description": "Move actor to a new location and rotate it",
                "inputs": [{"name": "NewLocation", "type": "Vector"}],
                "outputs": [],
                "complexity": "medium",
                "expected_nodes": ["SetActorLocation", "SetActorRotation", "PrintString"]
            },
            {
                "function_name": "TestConditionalFunction",
                "description": "Check if player health is greater than 50 and heal if needed",
                "inputs": [{"name": "CurrentHealth", "type": "float"}],
                "outputs": [{"name": "WasHealed", "type": "bool"}],
                "complexity": "complex",
                "expected_nodes": ["Greater_FloatFloat", "Branch", "PrintString"]
            },
            {
                "function_name": "TestStringFunction",
                "description": "Concatenate player name with score and display message",
                "inputs": [{"name": "PlayerName", "type": "string"}, {"name": "Score", "type": "int32"}],
                "outputs": [{"name": "Message", "type": "string"}],
                "complexity": "medium",
                "expected_nodes": ["Append", "FormatText", "PrintString"]
            }
        ]

        test_results = []
        total_nodes = 0
        total_connections = 0

        for i, test_case in enumerate(test_cases):
            log.log_info(f"[AI_TEST] Running test case {i+1}: {test_case['function_name']}")

            test_command = {
                "function_name": test_case["function_name"],
                "description": test_case["description"],
                "inputs": test_case["inputs"],
                "outputs": test_case["outputs"],
                "complexity": test_case["complexity"],
                "use_current_context": True
            }

            result = handle_generate_smart_blueprint_function(test_command)

            if result.get("success"):
                nodes_created = result.get("nodes_created", 0)
                connections_made = result.get("connections_made", 0)
                total_nodes += nodes_created
                total_connections += connections_made

                test_results.append({
                    "test_case": test_case["function_name"],
                    "success": True,
                    "nodes_created": nodes_created,
                    "connections_made": connections_made,
                    "description": test_case["description"]
                })

                log.log_info(f"[AI_TEST] ✅ {test_case['function_name']}: {nodes_created} nodes, {connections_made} connections")
            else:
                test_results.append({
                    "test_case": test_case["function_name"],
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "description": test_case["description"]
                })
                log.log_error(f"[AI_TEST] ❌ {test_case['function_name']} failed: {result.get('error')}")

        # Calculate success rate
        successful_tests = len([r for r in test_results if r.get("success")])
        success_rate = (successful_tests / len(test_cases)) * 100

        overall_success = success_rate >= 75  # Consider successful if 75% or more tests pass

        summary = {
            "success": overall_success,
            "message": f"AI-driven node creation test completed! {successful_tests}/{len(test_cases)} tests passed ({success_rate:.1f}%)",
            "total_nodes_created": total_nodes,
            "total_connections_made": total_connections,
            "success_rate": success_rate,
            "test_results": test_results
        }

        if overall_success:
            log.log_info(f"[AI_TEST] ✅ Overall test PASSED! {successful_tests}/{len(test_cases)} tests successful")
        else:
            log.log_warning(f"[AI_TEST] ⚠️ Overall test needs improvement. {successful_tests}/{len(test_cases)} tests successful")

        return summary

    except Exception as e:
        log.log_error(f"Error in AI test_automatic_node_creation: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_generate_context_aware_blueprint_function(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to generate a context-aware Blueprint function - LOCAL IMPLEMENTATION
    Enhanced version that uses current Blueprint context for smarter generation
    
    Args:
        command: The command dictionary containing:
            - function_name: Name for the new function (optional, will be generated)
            - description: Description of what the function should do
            - complexity: Function complexity (optional)
            
    Returns:
        Response dictionary with success/failure status and context information
    """
    try:
        description = command.get("description", "")
        function_name = command.get("function_name", "")
        complexity = command.get("complexity", "medium")

        log.log_command("generate_context_aware_blueprint_function", f"Description: {description}")

        # Generate function name from description if not provided
        if not function_name:
            function_name = _generate_function_name_from_description(description)

        # Get current Blueprint context
        current_blueprint_path = ""
        try:
            level_bp = unreal.EditorLevelLibrary.get_level_script_blueprint()
            if level_bp:
                current_blueprint_path = level_bp.get_path_name()
                blueprint_name = level_bp.get_name()
            else:
                current_blueprint_path = "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson"
                blueprint_name = "Lvl_ThirdPerson"
        except:
            current_blueprint_path = "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson"
            blueprint_name = "Lvl_ThirdPerson"

        log.log_info(f"Context-aware generation for Blueprint: {blueprint_name}")

        # Enhance inputs/outputs based on description context
        enhanced_inputs, enhanced_outputs = _analyze_description_for_parameters(description)

        # Create the enhanced command
        enhanced_command = {
            "function_name": function_name,
            "description": description,
            "inputs": enhanced_inputs,
            "outputs": enhanced_outputs,
            "complexity": complexity,
            "blueprint_path": current_blueprint_path,
            "use_current_context": True
        }

        # Generate the function using smart generation
        result = handle_generate_smart_blueprint_function(enhanced_command)

        if result.get("success"):
            # Add context information to the result
            result["context_aware"] = True
            result["blueprint_context"] = {
                "blueprint_name": blueprint_name,
                "blueprint_path": current_blueprint_path,
                "auto_generated_name": not bool(command.get("function_name")),
                "enhanced_parameters": {
                    "inputs": enhanced_inputs,
                    "outputs": enhanced_outputs
                }
            }
            log.log_result("generate_context_aware_blueprint_function", True, 
                          f"Context-aware function: {function_name}")
        else:
            log.log_error(f"Context-aware function generation failed: {result.get('error')}")

        return result

    except Exception as e:
        log.log_error(f"Error generating context-aware Blueprint function: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def _get_safe_node_types() -> List[str]:
    """
    Return a list of known safe node types that work with our current implementation
    Updated to use exact names that match our C++ mappings
    """
    # Start with confirmed working node types (using exact C++ mapping names)
    safe_types = [
        "PrintString",      # Maps to KismetSystemLibrary.PrintString
        "Print String",     # Also maps to PrintString via C++ mapping
        "Branch",           # Maps to UK2Node_IfThenElse
        "Add",              # Maps to KismetMathLibrary.Add_FloatFloat
        "Multiply",         # Maps to KismetMathLibrary.Multiply_FloatFloat
        "RandomIntegerInRange", # Maps to KismetMathLibrary.RandomIntegerInRange
        "Random Integer in Range", # Also maps via C++ mapping
        "Append",           # Maps to KismetStringLibrary.Concat_StrStr
        "Delay",            # Maps to KismetSystemLibrary.Delay
    ]
    
    return safe_types


def _validate_node_type(node_type: str) -> bool:
    """
    Validate if a node type is safe to use
    """
    safe_types = _get_safe_node_types()
    return node_type in safe_types


def _generate_nodes_for_function(function_name: str, description: str, inputs: List[Dict], outputs: List[Dict], complexity: str) -> List[Dict]:
    """
    AI-DRIVEN node generation for ANY type of Blueprint function.
    Analyzes the function description and intelligently selects appropriate nodes and logic flow.
    """
    nodes = []
    desc_lower = description.lower()
    log.log_info(f"[AI_GEN] Analyzing function '{function_name}' for intelligent node generation")
    log.log_info(f"[AI_GEN] Description: {description}")
    log.log_info(f"[AI_GEN] Inputs: {inputs} | Outputs: {outputs} | Complexity: {complexity}")

    # Position tracking for intelligent node layout
    current_x = 300
    current_y = 0
    node_spacing_x = 300
    node_spacing_y = 100

    # === AI-DRIVEN LOGIC ANALYSIS ===
    # Analyze the description to understand what the function should do
    logic_analysis = _analyze_function_logic(description, inputs, outputs, complexity)
    log.log_info(f"[AI_GEN] Logic analysis result: {logic_analysis}")

    # Generate nodes based on AI analysis
    nodes = _generate_nodes_from_analysis(logic_analysis, current_x, current_y, node_spacing_x, node_spacing_y)

    # === RANDOM NUMBER GENERATION ===
    if "random" in desc_lower:
        if "int" in desc_lower or "integer" in desc_lower or "number" in desc_lower:
            log.log_info(f"[GEN] Adding RandomIntegerInRange node")
            nodes.append({
                "node_type": "RandomIntegerInRange",
                "node_id": f"random_int_{len(nodes)}",
                "node_position": [current_x, current_y],
                "node_properties": {
                    "Min": 1,
                    "Max": 100
                }
            })
            current_x += node_spacing_x
        elif "float" in desc_lower or "decimal" in desc_lower:
            log.log_info(f"[GEN] Adding RandomFloatInRange node")
            nodes.append({
                "node_type": "RandomFloatInRange",
                "node_id": f"random_float_{len(nodes)}",
                "node_position": [current_x, current_y],
                "node_properties": {
                    "Min": 0.0,
                    "Max": 1.0
                }
            })
            current_x += node_spacing_x

    # === STRING OPERATIONS ===
    if "string" in desc_lower or "text" in desc_lower:
        if "append" in desc_lower or "concat" in desc_lower or "combine" in desc_lower:
            log.log_info(f"[GEN] Adding Append (string concatenation) node")
            nodes.append({
                "node_type": "Append",
                "node_id": f"append_{len(nodes)}",
                "node_position": [current_x, current_y],
                "node_properties": {}
            })
            current_x += node_spacing_x

        if "length" in desc_lower or "len" in desc_lower:
            log.log_info(f"[GEN] Adding StringLength node")
            nodes.append({
                "node_type": "StringLength",
                "node_id": f"string_length_{len(nodes)}",
                "node_position": [current_x, current_y],
                "node_properties": {}
            })
            current_x += node_spacing_x

        if "literal" in desc_lower or "make" in desc_lower:
            log.log_info(f"[GEN] Adding MakeLiteralString node")
            nodes.append({
                "node_type": "MakeLiteralString",
                "node_id": f"literal_string_{len(nodes)}",
                "node_position": [current_x, current_y],
                "node_properties": {
                    "Value": "Hello World"
                }
            })
            current_x += node_spacing_x

    # === MATH OPERATIONS ===
    if "add" in desc_lower or "plus" in desc_lower or "sum" in desc_lower:
        if "float" in desc_lower:
            log.log_info(f"[GEN] Adding Add_FloatFloat node")
            nodes.append({
                "node_type": "Add_FloatFloat",
                "node_id": f"add_float_{len(nodes)}",
                "node_position": [current_x, current_y],
                "node_properties": {}
            })
        else:
            log.log_info(f"[GEN] Adding Add_IntInt node")
            nodes.append({
                "node_type": "Add_IntInt",
                "node_id": f"add_int_{len(nodes)}",
                "node_position": [current_x, current_y],
                "node_properties": {}
            })
        current_x += node_spacing_x

    if "multiply" in desc_lower or "times" in desc_lower:
        log.log_info(f"[GEN] Adding Multiply_FloatFloat node")
        nodes.append({
            "node_type": "Multiply_FloatFloat",
            "node_id": f"multiply_{len(nodes)}",
            "node_position": [current_x, current_y],
            "node_properties": {}
        })
        current_x += node_spacing_x

    # === CONTROL FLOW ===
    if "loop" in desc_lower or "iterate" in desc_lower:
        if "for" in desc_lower:
            log.log_info(f"[GEN] Adding ForLoop node")
            nodes.append({
                "node_type": "ForLoop",
                "node_id": f"for_loop_{len(nodes)}",
                "node_position": [current_x, current_y],
                "node_properties": {
                    "FirstIndex": 0,
                    "LastIndex": 10
                }
            })
            current_x += node_spacing_x

    if "if" in desc_lower or "condition" in desc_lower or "branch" in desc_lower:
        log.log_info(f"[GEN] Adding Branch node")
        nodes.append({
            "node_type": "Branch",
            "node_id": f"branch_{len(nodes)}",
            "node_position": [current_x, current_y],
            "node_properties": {}
        })
        current_x += node_spacing_x

    # === PRINT/DEBUG OUTPUT ===
    if "print" in desc_lower or "log" in desc_lower or "screen" in desc_lower or "debug" in desc_lower:
        log.log_info(f"[GEN] Adding PrintString node")
        nodes.append({
            "node_type": "PrintString",
            "node_id": f"print_{len(nodes)}",
            "node_position": [current_x, current_y],
            "node_properties": {
                "InString": "Debug Output",
                "bPrintToScreen": True,
                "bPrintToLog": True,
                "Duration": 2.0
            }
        })
        current_x += node_spacing_x

    # === VECTOR OPERATIONS ===
    if "vector" in desc_lower or "location" in desc_lower or "position" in desc_lower:
        if "make" in desc_lower or "create" in desc_lower:
            log.log_info(f"[GEN] Adding MakeVector node")
            nodes.append({
                "node_type": "MakeVector",
                "node_id": f"make_vector_{len(nodes)}",
                "node_position": [current_x, current_y],
                "node_properties": {
                    "X": 0.0,
                    "Y": 0.0,
                    "Z": 0.0
                }
            })
            current_x += node_spacing_x

    # === COMPLEXITY-BASED NODE ADDITION ===
    if complexity == "complex":
        # Add more sophisticated nodes for complex functions
        if not any("delay" in node.get("node_type", "").lower() for node in nodes):
            log.log_info(f"[GEN] Adding Delay node for complex function")
            nodes.append({
                "node_type": "Delay",
                "node_id": f"delay_{len(nodes)}",
                "node_position": [current_x, current_y + node_spacing_y],
                "node_properties": {
                    "Duration": 1.0
                }
            })

    # === FALLBACK: ENSURE AT LEAST ONE FUNCTIONAL NODE ===
    if not nodes:
        log.log_info(f"[GEN] No specific nodes generated, adding default PrintString node")
        nodes.append({
            "node_type": "PrintString",
            "node_id": f"default_print_{len(nodes)}",
            "node_position": [300, 0],
            "node_properties": {
                "InString": f"Function {function_name} executed",
                "bPrintToScreen": True,
                "bPrintToLog": True,
                "Duration": 2.0
            }
        })

    # === OUTPUT HANDLING ===
    # Add return node if outputs are specified
    if outputs:
        current_x += node_spacing_x
        log.log_info(f"[GEN] Adding return node for {len(outputs)} outputs")

        # Configure return node with output pin information
        return_properties = {
            "outputs": outputs,  # Pass output configuration to C++
            "output_count": len(outputs)
        }

        nodes.append({
            "node_type": "K2Node_FunctionResult",
            "node_id": f"return_{len(nodes)}",
            "node_position": [current_x, current_y],
            "node_properties": return_properties
        })

    log.log_info(f"[AI_GEN] Generated {len(nodes)} nodes based on AI analysis")
    return nodes

def _analyze_function_logic(description: str, inputs: List[Dict], outputs: List[Dict], complexity: str) -> Dict[str, Any]:
    """
    AI-driven analysis of function description to determine required logic and nodes.
    This is the core intelligence that decides what nodes and connections are needed.
    """
    desc_lower = description.lower()
    words = desc_lower.split()

    analysis = {
        "intent": "unknown",
        "operations": [],
        "data_flow": [],
        "control_flow": [],
        "required_nodes": [],
        "connections": [],
        "variables_needed": [],
        "events_needed": []
    }

    log.log_info(f"[AI_ANALYSIS] Analyzing: '{description}'")

    # === INTENT DETECTION ===
    # Determine the primary purpose of the function
    if any(word in desc_lower for word in ["calculate", "compute", "math", "add", "multiply", "divide", "subtract"]):
        analysis["intent"] = "mathematical"
    elif any(word in desc_lower for word in ["move", "location", "position", "transform", "rotate", "scale"]):
        analysis["intent"] = "transformation"
    elif any(word in desc_lower for word in ["spawn", "create", "instantiate", "destroy", "delete"]):
        analysis["intent"] = "object_management"
    elif any(word in desc_lower for word in ["check", "test", "validate", "compare", "condition", "if"]):
        analysis["intent"] = "conditional"
    elif any(word in desc_lower for word in ["loop", "repeat", "iterate", "for", "while", "each"]):
        analysis["intent"] = "iterative"
    elif any(word in desc_lower for word in ["input", "key", "mouse", "button", "press"]):
        analysis["intent"] = "input_handling"
    elif any(word in desc_lower for word in ["ui", "widget", "menu", "button", "text", "display"]):
        analysis["intent"] = "ui_interaction"
    elif any(word in desc_lower for word in ["sound", "audio", "play", "music", "volume"]):
        analysis["intent"] = "audio"
    elif any(word in desc_lower for word in ["animation", "animate", "tween", "lerp", "interpolate"]):
        analysis["intent"] = "animation"
    elif any(word in desc_lower for word in ["save", "load", "file", "data", "persistent"]):
        analysis["intent"] = "data_persistence"
    elif any(word in desc_lower for word in ["network", "multiplayer", "server", "client", "rpc"]):
        analysis["intent"] = "networking"
    elif any(word in desc_lower for word in ["physics", "collision", "gravity", "force", "impulse"]):
        analysis["intent"] = "physics"
    elif any(word in desc_lower for word in ["material", "texture", "shader", "color", "render"]):
        analysis["intent"] = "rendering"
    elif any(word in desc_lower for word in ["timer", "delay", "wait", "schedule", "time"]):
        analysis["intent"] = "timing"
    elif any(word in desc_lower for word in ["random", "chance", "probability", "luck"]):
        analysis["intent"] = "randomization"
    elif any(word in desc_lower for word in ["string", "text", "message", "format", "parse"]):
        analysis["intent"] = "string_processing"
    elif any(word in desc_lower for word in ["array", "list", "collection", "sort", "filter"]):
        analysis["intent"] = "data_structures"
    elif any(word in desc_lower for word in ["print", "log", "debug", "output", "display"]):
        analysis["intent"] = "debugging"
    else:
        # Try to infer from inputs/outputs
        if inputs and outputs:
            analysis["intent"] = "data_processing"
        elif outputs:
            analysis["intent"] = "data_generation"
        else:
            analysis["intent"] = "action_execution"

    log.log_info(f"[AI_ANALYSIS] Detected intent: {analysis['intent']}")

    # === OPERATION DETECTION ===
    # Detect specific operations needed
    operations = []

    # Mathematical operations
    if "add" in desc_lower or "plus" in desc_lower or "sum" in desc_lower:
        operations.append({"type": "math", "operation": "add", "data_type": "float"})
    if "subtract" in desc_lower or "minus" in desc_lower:
        operations.append({"type": "math", "operation": "subtract", "data_type": "float"})
    if "multiply" in desc_lower or "times" in desc_lower:
        operations.append({"type": "math", "operation": "multiply", "data_type": "float"})
    if "divide" in desc_lower or "division" in desc_lower:
        operations.append({"type": "math", "operation": "divide", "data_type": "float"})

    # String operations
    if "concat" in desc_lower or "combine" in desc_lower or "append" in desc_lower:
        operations.append({"type": "string", "operation": "concatenate"})
    if "length" in desc_lower or "size" in desc_lower:
        operations.append({"type": "string", "operation": "length"})
    if "format" in desc_lower:
        operations.append({"type": "string", "operation": "format"})

    # Comparison operations
    if any(word in desc_lower for word in ["equal", "same", "match", "=="]):
        operations.append({"type": "comparison", "operation": "equal"})
    if any(word in desc_lower for word in ["greater", "larger", "bigger", ">"]):
        operations.append({"type": "comparison", "operation": "greater"})
    if any(word in desc_lower for word in ["less", "smaller", "<"]):
        operations.append({"type": "comparison", "operation": "less"})

    # Random operations
    if "random" in desc_lower:
        if "int" in desc_lower or "integer" in desc_lower:
            operations.append({"type": "random", "operation": "integer", "range": True})
        elif "float" in desc_lower or "decimal" in desc_lower:
            operations.append({"type": "random", "operation": "float", "range": True})
        else:
            operations.append({"type": "random", "operation": "generic"})

    analysis["operations"] = operations
    log.log_info(f"[AI_ANALYSIS] Detected operations: {operations}")

    return analysis

def _generate_nodes_from_analysis(analysis: Dict[str, Any], start_x: int, start_y: int, spacing_x: int, spacing_y: int) -> List[Dict]:
    """
    Generate Blueprint nodes based on AI analysis of function requirements.
    This creates the actual node definitions that will be sent to C++ for creation.
    """
    nodes = []
    current_x = start_x
    current_y = start_y

    intent = analysis.get("intent", "unknown")
    operations = analysis.get("operations", [])

    log.log_info(f"[AI_NODE_GEN] Generating nodes for intent: {intent}")

    # === INTENT-BASED NODE GENERATION ===
    if intent == "mathematical":
        nodes.extend(_generate_math_nodes(operations, current_x, current_y, spacing_x))
        current_x += spacing_x * len([op for op in operations if op.get("type") == "math"])

    elif intent == "transformation":
        nodes.extend(_generate_transformation_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "object_management":
        nodes.extend(_generate_object_management_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "conditional":
        nodes.extend(_generate_conditional_nodes(operations, current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "iterative":
        nodes.extend(_generate_loop_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "input_handling":
        nodes.extend(_generate_input_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "ui_interaction":
        nodes.extend(_generate_ui_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "audio":
        nodes.extend(_generate_audio_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "animation":
        nodes.extend(_generate_animation_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "data_persistence":
        nodes.extend(_generate_data_persistence_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "networking":
        nodes.extend(_generate_networking_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "physics":
        nodes.extend(_generate_physics_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "rendering":
        nodes.extend(_generate_rendering_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "timing":
        nodes.extend(_generate_timing_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "randomization":
        nodes.extend(_generate_random_nodes(operations, current_x, current_y, spacing_x))
        current_x += spacing_x * len([op for op in operations if op.get("type") == "random"])

    elif intent == "string_processing":
        nodes.extend(_generate_string_nodes(operations, current_x, current_y, spacing_x))
        current_x += spacing_x * len([op for op in operations if op.get("type") == "string"])

    elif intent == "data_structures":
        nodes.extend(_generate_data_structure_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    elif intent == "debugging":
        nodes.extend(_generate_debug_nodes(current_x, current_y, spacing_x))
        current_x += spacing_x

    else:
        # Default: create basic functional nodes
        nodes.extend(_generate_default_nodes(operations, current_x, current_y, spacing_x))
        current_x += spacing_x * 2

    # === OPERATION-SPECIFIC NODES ===
    # Add additional nodes based on specific operations detected
    for operation in operations:
        if operation.get("type") == "comparison":
            nodes.append({
                "node_type": "Branch",
                "node_id": f"comparison_{len(nodes)}",
                "node_position": [current_x, current_y],
                "node_properties": {}
            })
            current_x += spacing_x

    # === ALWAYS ADD DEBUG OUTPUT ===
    # Every function should have some form of output/feedback
    if not any("PrintString" in node.get("node_type", "") for node in nodes):
        nodes.append({
            "node_type": "PrintString",
            "node_id": f"debug_output_{len(nodes)}",
            "node_position": [current_x, current_y],
            "node_properties": {
                "InString": "Function executed successfully",
                "bPrintToScreen": True,
                "bPrintToLog": True,
                "Duration": 2.0
            }
        })

    log.log_info(f"[AI_NODE_GEN] Generated {len(nodes)} nodes: {[node.get('node_type') for node in nodes]}")
    return nodes

# === SPECIALIZED NODE GENERATORS FOR DIFFERENT INTENTS ===

def _generate_math_nodes(operations: List[Dict], x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for mathematical operations"""
    nodes = []
    current_x = x

    for op in operations:
        if op.get("type") == "math":
            operation = op.get("operation")
            data_type = op.get("data_type", "float")

            if operation == "add":
                node_type = "Add_FloatFloat" if data_type == "float" else "Add_IntInt"
            elif operation == "subtract":
                node_type = "Subtract_FloatFloat" if data_type == "float" else "Subtract_IntInt"
            elif operation == "multiply":
                node_type = "Multiply_FloatFloat" if data_type == "float" else "Multiply_IntInt"
            elif operation == "divide":
                node_type = "Divide_FloatFloat" if data_type == "float" else "Divide_IntInt"
            else:
                node_type = "Add_FloatFloat"  # Default

            nodes.append({
                "node_type": node_type,
                "node_id": f"math_{operation}_{len(nodes)}",
                "node_position": [current_x, y],
                "node_properties": {}
            })
            current_x += spacing

    return nodes

def _generate_transformation_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for object transformation (movement, rotation, scaling)"""
    return [
        {
            "node_type": "GetActorLocation",
            "node_id": f"get_location_{x}",
            "node_position": [x, y],
            "node_properties": {}
        },
        {
            "node_type": "SetActorLocation",
            "node_id": f"set_location_{x}",
            "node_position": [x + spacing, y],
            "node_properties": {}
        }
    ]

def _generate_object_management_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for spawning/destroying objects"""
    return [
        {
            "node_type": "SpawnActor",
            "node_id": f"spawn_actor_{x}",
            "node_position": [x, y],
            "node_properties": {}
        },
        {
            "node_type": "DestroyActor",
            "node_id": f"destroy_actor_{x}",
            "node_position": [x + spacing, y],
            "node_properties": {}
        }
    ]

def _generate_conditional_nodes(operations: List[Dict], x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for conditional logic"""
    nodes = []

    # Always add a branch node for conditionals
    nodes.append({
        "node_type": "Branch",
        "node_id": f"branch_{x}",
        "node_position": [x, y],
        "node_properties": {}
    })

    # Add comparison nodes based on operations
    for op in operations:
        if op.get("type") == "comparison":
            comparison_type = op.get("operation", "equal")
            if comparison_type == "equal":
                node_type = "EqualEqual_FloatFloat"
            elif comparison_type == "greater":
                node_type = "Greater_FloatFloat"
            elif comparison_type == "less":
                node_type = "Less_FloatFloat"
            else:
                node_type = "EqualEqual_FloatFloat"

            nodes.append({
                "node_type": node_type,
                "node_id": f"compare_{comparison_type}_{len(nodes)}",
                "node_position": [x + spacing, y],
                "node_properties": {}
            })

    return nodes

def _generate_loop_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for loops and iteration"""
    return [
        {
            "node_type": "ForLoop",
            "node_id": f"for_loop_{x}",
            "node_position": [x, y],
            "node_properties": {
                "FirstIndex": 0,
                "LastIndex": 10
            }
        }
    ]

def _generate_input_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for input handling"""
    return [
        {
            "node_type": "InputAction",
            "node_id": f"input_action_{x}",
            "node_position": [x, y],
            "node_properties": {}
        }
    ]

def _generate_random_nodes(operations: List[Dict], x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for random operations"""
    nodes = []
    current_x = x

    for op in operations:
        if op.get("type") == "random":
            operation = op.get("operation", "generic")

            if operation == "integer":
                node_type = "RandomIntegerInRange"
                properties = {"Min": 1, "Max": 100}
            elif operation == "float":
                node_type = "RandomFloatInRange"
                properties = {"Min": 0.0, "Max": 1.0}
            else:
                node_type = "RandomFloat"
                properties = {}

            nodes.append({
                "node_type": node_type,
                "node_id": f"random_{operation}_{len(nodes)}",
                "node_position": [current_x, y],
                "node_properties": properties
            })
            current_x += spacing

    return nodes

def _generate_string_nodes(operations: List[Dict], x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for string operations"""
    nodes = []
    current_x = x

    for op in operations:
        if op.get("type") == "string":
            operation = op.get("operation")

            if operation == "concatenate":
                node_type = "Append"
            elif operation == "length":
                node_type = "StringLength"
            elif operation == "format":
                node_type = "FormatText"
            else:
                node_type = "MakeLiteralString"

            nodes.append({
                "node_type": node_type,
                "node_id": f"string_{operation}_{len(nodes)}",
                "node_position": [current_x, y],
                "node_properties": {}
            })
            current_x += spacing

    return nodes

def _generate_ui_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for UI interactions"""
    return [
        {
            "node_type": "CreateWidget",
            "node_id": f"create_widget_{x}",
            "node_position": [x, y],
            "node_properties": {}
        }
    ]

def _generate_audio_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for audio operations"""
    return [
        {
            "node_type": "PlaySound2D",
            "node_id": f"play_sound_{x}",
            "node_position": [x, y],
            "node_properties": {}
        }
    ]

def _generate_animation_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for animation operations"""
    return [
        {
            "node_type": "PlayAnimation",
            "node_id": f"play_animation_{x}",
            "node_position": [x, y],
            "node_properties": {}
        }
    ]

def _generate_data_persistence_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for data saving/loading"""
    return [
        {
            "node_type": "SaveGameToSlot",
            "node_id": f"save_game_{x}",
            "node_position": [x, y],
            "node_properties": {}
        }
    ]

def _generate_networking_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for networking operations"""
    return [
        {
            "node_type": "ServerRPC",
            "node_id": f"server_rpc_{x}",
            "node_position": [x, y],
            "node_properties": {}
        }
    ]

def _generate_physics_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for physics operations"""
    return [
        {
            "node_type": "AddImpulse",
            "node_id": f"add_impulse_{x}",
            "node_position": [x, y],
            "node_properties": {}
        }
    ]

def _generate_rendering_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for rendering operations"""
    return [
        {
            "node_type": "SetMaterial",
            "node_id": f"set_material_{x}",
            "node_position": [x, y],
            "node_properties": {}
        }
    ]

def _generate_timing_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for timing operations"""
    return [
        {
            "node_type": "Delay",
            "node_id": f"delay_{x}",
            "node_position": [x, y],
            "node_properties": {
                "Duration": 1.0
            }
        }
    ]

def _generate_data_structure_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for data structure operations"""
    return [
        {
            "node_type": "MakeArray",
            "node_id": f"make_array_{x}",
            "node_position": [x, y],
            "node_properties": {}
        }
    ]

def _generate_debug_nodes(x: int, y: int, spacing: int) -> List[Dict]:
    """Generate nodes for debugging operations"""
    return [
        {
            "node_type": "PrintString",
            "node_id": f"debug_print_{x}",
            "node_position": [x, y],
            "node_properties": {
                "InString": "Debug output",
                "bPrintToScreen": True,
                "bPrintToLog": True,
                "Duration": 2.0
            }
        }
    ]

def _generate_default_nodes(operations: List[Dict], x: int, y: int, spacing: int) -> List[Dict]:
    """Generate default nodes when intent is unclear"""
    nodes = []

    # If we have operations, try to create nodes for them
    if operations:
        for i, op in enumerate(operations):
            nodes.append({
                "node_type": "PrintString",
                "node_id": f"default_op_{i}",
                "node_position": [x + (i * spacing), y],
                "node_properties": {
                    "InString": f"Operation: {op.get('type', 'unknown')}",
                    "bPrintToScreen": True,
                    "bPrintToLog": True,
                    "Duration": 2.0
                }
            })
    else:
        # Fallback: simple print node
        nodes.append({
            "node_type": "PrintString",
            "node_id": f"default_print_{x}",
            "node_position": [x, y],
            "node_properties": {
                "InString": "Function executed",
                "bPrintToScreen": True,
                "bPrintToLog": True,
                "Duration": 2.0
            }
        })

    return nodes


def _generate_connections_for_nodes(nodes: List[Dict], node_mapping: Dict[str, str], inputs: List[Dict], outputs: List[Dict]) -> List[Dict]:
    """
    AI-DRIVEN connection generation for Blueprint function graphs.
    Intelligently determines how nodes should be connected based on their types and data flow requirements.
    """
    connections = []
    log.log_info(f"[AI_CONN] Generating intelligent connections for {len(nodes)} nodes")

    if not nodes:
        log.log_info("[AI_CONN] No nodes to connect")
        return connections

    # === AI-DRIVEN CONNECTION ANALYSIS ===
    connection_plan = _analyze_node_connections(nodes, inputs, outputs)
    log.log_info(f"[AI_CONN] Connection plan: {connection_plan}")

    # Generate connections based on AI analysis
    connections = _create_connections_from_plan(connection_plan, node_mapping)

    # === FALLBACK: SIMPLE MATH CONNECTIONS ===
    if len(connections) == 0:
        log.log_info("[AI_CONN] AI connections failed, using simple math fallback")
        connections = _create_simple_math_connections(nodes, node_mapping, inputs, outputs)

    log.log_info(f"[AI_CONN] Generated {len(connections)} intelligent connections")
    return connections

def _create_simple_math_connections(nodes: List[Dict], node_mapping: Dict[str, str], inputs: List[Dict], outputs: List[Dict]) -> List[Dict]:
    """
    Simple fallback connection logic for basic math operations.
    Creates connections for: Entry -> Math Node -> Return Node
    """
    connections = []

    # Find different node types
    data_nodes = []  # Math, random, etc.
    output_nodes = []  # Print, etc.
    return_nodes = []

    log.log_info(f"[SIMPLE_CONN] Analyzing {len(nodes)} nodes for simple connections")

    for node in nodes:
        # Handle both dict and string cases
        if isinstance(node, dict):
            node_type = node.get("node_type", "")
            log.log_info(f"[SIMPLE_CONN] Processing node: {node_type}")
        elif isinstance(node, str):
            log.log_warning(f"[SIMPLE_CONN] Received string instead of dict: {node}")
            continue
        else:
            log.log_warning(f"[SIMPLE_CONN] Unknown node type: {type(node)}")
            continue

        # Categorize nodes
        if node_type in ["Add_FloatFloat", "Add_IntInt", "Multiply_FloatFloat", "Subtract_FloatFloat", "Divide_FloatFloat", "RandomIntegerInRange", "RandomFloatInRange"]:
            data_nodes.append(node)
        elif node_type in ["PrintString", "LogString"]:
            output_nodes.append(node)
        elif node_type == "K2Node_FunctionResult":
            return_nodes.append(node)

    log.log_info(f"[SIMPLE_CONN] Found {len(data_nodes)} data nodes, {len(output_nodes)} output nodes, {len(return_nodes)} return nodes")

    # Create simple connections based on available nodes
    if len(data_nodes) > 0 and len(return_nodes) > 0:
        data_node = data_nodes[0]
        return_node = return_nodes[0]

        data_guid = node_mapping.get(data_node.get("node_id"))
        return_guid = node_mapping.get(return_node.get("node_id"))

        log.log_info(f"[SIMPLE_CONN] Data node GUID: {data_guid}, Return node GUID: {return_guid}")

        if data_guid and return_guid:
            # For RandomIntegerInRange: Entry -> Random -> Return
            data_node_type = data_node.get("node_type", "")

            if data_node_type == "RandomIntegerInRange":
                # Connection 1: Entry -> Random Node (execution) - Skip for pure functions
                # Connection 2: Random result -> Return Node (data)
                connections.append({
                    "from_node": data_guid,
                    "from_pin": "ReturnValue",
                    "to_node": return_guid,
                    "to_pin": outputs[0].get("name", "RandomNumber") if outputs else "RandomNumber"
                })
                log.log_info(f"[SIMPLE_CONN] Created RandomIntegerInRange -> Return connection")

            # For math operations: Entry -> Math -> Return
            elif data_node_type in ["Add_FloatFloat", "Add_IntInt", "Multiply_FloatFloat"]:
                # Connection 1: Entry -> Math Node (execution)
                connections.append({
                    "from_node": "entry",
                    "from_pin": "then",
                    "to_node": data_guid,
                    "to_pin": "execute"
                })

                # Connection 2: Math Node -> Return Node (execution)
                connections.append({
                    "from_node": data_guid,
                    "from_pin": "then",
                    "to_node": return_guid,
                    "to_pin": "execute"
                })

                # Connection 3: Function inputs -> Math Node inputs (data)
                if len(inputs) >= 2:
                    connections.append({
                        "from_node": "entry",
                        "from_pin": inputs[0].get("name", "A"),
                        "to_node": data_guid,
                        "to_pin": "A"
                    })
                    connections.append({
                        "from_node": "entry",
                        "from_pin": inputs[1].get("name", "B"),
                        "to_node": data_guid,
                        "to_pin": "B"
                    })

                # Connection 4: Math result -> Return Node (data)
                if len(outputs) > 0:
                    connections.append({
                        "from_node": data_guid,
                        "from_pin": "ReturnValue",
                        "to_node": return_guid,
                        "to_pin": outputs[0].get("name", "Result")
                    })

            log.log_info(f"[SIMPLE_CONN] Created {len(connections)} simple connections for {data_node_type}")

    return connections

def _analyze_node_connections(nodes: List[Dict], inputs: List[Dict], outputs: List[Dict]) -> Dict[str, Any]:
    """
    AI analysis to determine how nodes should be connected based on their types and data flow.
    """
    plan = {
        "execution_flow": [],
        "data_flow": [],
        "node_roles": {},
        "connection_strategy": "intelligent"
    }

    # Debug: Check what we received
    log.log_info(f"[AI_CONN_ANALYSIS] Received {len(nodes)} nodes for analysis")
    for i, node in enumerate(nodes):
        log.log_info(f"[AI_CONN_ANALYSIS] Node {i}: {type(node)} = {node}")

    # Categorize nodes by their roles
    for node in nodes:
        # Handle both dict and string cases
        if isinstance(node, dict):
            node_type = node.get("node_type", "")
            node_id = node.get("node_id", "")
        elif isinstance(node, str):
            log.log_warning(f"[AI_CONN_ANALYSIS] Received string instead of dict: {node}")
            continue
        else:
            log.log_warning(f"[AI_CONN_ANALYSIS] Unknown node type: {type(node)}")
            continue

        # Determine node role based on type
        if node_type in ["RandomIntegerInRange", "RandomFloatInRange", "RandomFloat", "RandomInteger"]:
            plan["node_roles"][node_id] = "data_generator"
        elif node_type in ["Add_FloatFloat", "Add_IntInt", "Multiply_FloatFloat", "Subtract_FloatFloat", "Divide_FloatFloat"]:
            plan["node_roles"][node_id] = "data_processor"
        elif node_type in ["PrintString", "LogString"]:
            plan["node_roles"][node_id] = "output"
        elif node_type in ["Branch", "Switch"]:
            plan["node_roles"][node_id] = "control_flow"
        elif node_type in ["ForLoop", "WhileLoop"]:
            plan["node_roles"][node_id] = "iterator"
        elif node_type in ["Delay", "Timer"]:
            plan["node_roles"][node_id] = "timing"
        elif node_type in ["GetActorLocation", "GetActorRotation"]:
            plan["node_roles"][node_id] = "data_getter"
        elif node_type in ["SetActorLocation", "SetActorRotation"]:
            plan["node_roles"][node_id] = "data_setter"
        elif node_type in ["Append", "StringLength", "FormatText"]:
            plan["node_roles"][node_id] = "string_processor"
        elif node_type == "K2Node_FunctionResult":
            plan["node_roles"][node_id] = "return"
        else:
            plan["node_roles"][node_id] = "generic"

    # === INTELLIGENT EXECUTION FLOW PLANNING ===
    # Determine logical execution order based on node roles
    execution_order = []

    # 1. Data generators first (random, getters, etc.)
    data_generators = [nid for nid, role in plan["node_roles"].items() if role == "data_generator"]
    data_getters = [nid for nid, role in plan["node_roles"].items() if role == "data_getter"]
    execution_order.extend(data_generators + data_getters)

    # 2. Data processors (math, string operations)
    data_processors = [nid for nid, role in plan["node_roles"].items() if role == "data_processor"]
    string_processors = [nid for nid, role in plan["node_roles"].items() if role == "string_processor"]
    execution_order.extend(data_processors + string_processors)

    # 3. Control flow nodes
    control_flow = [nid for nid, role in plan["node_roles"].items() if role == "control_flow"]
    execution_order.extend(control_flow)

    # 4. Iterators
    iterators = [nid for nid, role in plan["node_roles"].items() if role == "iterator"]
    execution_order.extend(iterators)

    # 5. Data setters (actions that modify state)
    data_setters = [nid for nid, role in plan["node_roles"].items() if role == "data_setter"]
    execution_order.extend(data_setters)

    # 6. Timing nodes
    timing = [nid for nid, role in plan["node_roles"].items() if role == "timing"]
    execution_order.extend(timing)

    # 7. Output nodes (print, log)
    outputs = [nid for nid, role in plan["node_roles"].items() if role == "output"]
    execution_order.extend(outputs)

    # 8. Return nodes last
    returns = [nid for nid, role in plan["node_roles"].items() if role == "return"]
    execution_order.extend(returns)

    # 9. Any remaining generic nodes
    generic = [nid for nid, role in plan["node_roles"].items() if role == "generic"]
    execution_order.extend(generic)

    plan["execution_flow"] = execution_order

    # === INTELLIGENT DATA FLOW PLANNING ===
    # Determine which nodes should pass data to which other nodes
    data_connections = []

    # Connect data generators to processors
    for generator in data_generators:
        for processor in data_processors:
            data_connections.append({
                "from": generator,
                "to": processor,
                "from_pin": "ReturnValue",
                "to_pin": "A",  # First input pin
                "data_type": "auto"
            })
            break  # Connect to first available processor

    # Connect processors to outputs
    for processor in data_processors + string_processors:
        for output in outputs:
            data_connections.append({
                "from": processor,
                "to": output,
                "from_pin": "ReturnValue",
                "to_pin": "InString",
                "data_type": "auto"
            })
            break  # Connect to first available output

    # Connect data generators directly to outputs if no processors
    if not data_processors and not string_processors:
        for generator in data_generators + data_getters:
            for output in outputs:
                data_connections.append({
                    "from": generator,
                    "to": output,
                    "from_pin": "ReturnValue",
                    "to_pin": "InString",
                    "data_type": "auto"
                })
                break

    # Connect to return nodes if outputs are specified
    if returns and outputs:
        # Find the best data source for return value
        data_sources = data_processors + string_processors + data_generators + data_getters
        if data_sources:
            # Safely get the output pin name
            output_pin_name = "ReturnValue"
            if outputs and len(outputs) > 0:
                first_output = outputs[0]
                if isinstance(first_output, dict):
                    output_pin_name = first_output.get("name", "ReturnValue")
                elif isinstance(first_output, str):
                    output_pin_name = first_output

            data_connections.append({
                "from": data_sources[0],  # Use first available data source
                "to": returns[0],
                "from_pin": "ReturnValue",
                "to_pin": output_pin_name,
                "data_type": "auto"
            })

    plan["data_flow"] = data_connections

    log.log_info(f"[AI_CONN_ANALYSIS] Execution order: {execution_order}")
    log.log_info(f"[AI_CONN_ANALYSIS] Data connections: {len(data_connections)}")

    return plan

def _create_connections_from_plan(plan: Dict[str, Any], node_mapping: Dict[str, str]) -> List[Dict]:
    """
    Create actual Blueprint connections based on the AI-generated connection plan.
    """
    connections = []

    execution_flow = plan.get("execution_flow", [])
    data_flow = plan.get("data_flow", [])
    node_roles = plan.get("node_roles", {})

    log.log_info(f"[AI_CONN_CREATE] Creating connections from plan")

    # === CREATE EXECUTION FLOW CONNECTIONS ===
    current_exec_source = "entry"
    current_exec_pin = "then"

    for i, node_id in enumerate(execution_flow):
        actual_node_guid = node_mapping.get(node_id)
        if not actual_node_guid:
            log.log_warning(f"[AI_CONN_CREATE] No GUID found for node {node_id}")
            continue

        # Determine appropriate execution pins based on node role
        role = node_roles.get(node_id, "generic")
        exec_input_pin = _get_exec_input_pin(role)
        exec_output_pin = _get_exec_output_pin(role)

        # Create execution connection (skip first connection from entry to first node if it's a return node)
        if not (current_exec_source == "entry" and role == "return"):
            connections.append({
                "from_node": current_exec_source,
                "from_pin": current_exec_pin,
                "to_node": actual_node_guid,
                "to_pin": exec_input_pin
            })
            log.log_info(f"[AI_CONN_CREATE] Exec: {current_exec_source}.{current_exec_pin} -> {node_id}.{exec_input_pin}")

        # Update for next connection
        current_exec_source = actual_node_guid
        current_exec_pin = exec_output_pin

    # === CREATE DATA FLOW CONNECTIONS ===
    for data_conn in data_flow:
        from_node_id = data_conn.get("from")
        to_node_id = data_conn.get("to")
        from_pin = data_conn.get("from_pin", "ReturnValue")
        to_pin = data_conn.get("to_pin", "InString")

        from_guid = node_mapping.get(from_node_id)
        to_guid = node_mapping.get(to_node_id)

        if from_guid and to_guid:
            connections.append({
                "from_node": from_guid,
                "from_pin": from_pin,
                "to_node": to_guid,
                "to_pin": to_pin
            })
            log.log_info(f"[AI_CONN_CREATE] Data: {from_node_id}.{from_pin} -> {to_node_id}.{to_pin}")
        else:
            log.log_warning(f"[AI_CONN_CREATE] Missing GUID for data connection: {from_node_id} -> {to_node_id}")

    log.log_info(f"[AI_CONN_CREATE] Created {len(connections)} total connections")
    return connections

def _get_exec_input_pin(role: str) -> str:
    """Get the appropriate execution input pin name based on node role"""
    pin_mapping = {
        "control_flow": "exec",
        "iterator": "exec",
        "timing": "exec",
        "output": "exec",
        "data_setter": "exec",
        "return": "exec",
        "generic": "exec"
    }
    return pin_mapping.get(role, "exec")

def _get_exec_output_pin(role: str) -> str:
    """Get the appropriate execution output pin name based on node role"""
    pin_mapping = {
        "control_flow": "True",  # Branch nodes typically use True/False
        "iterator": "Completed",  # ForLoop uses Completed
        "timing": "then",
        "output": "then",
        "data_setter": "then",
        "data_generator": "then",
        "data_processor": "then",
        "string_processor": "then",
        "generic": "then"
    }
    return pin_mapping.get(role, "then")

    # Organize nodes by type for easier connection logic
    node_types = {}
    for node in nodes:
        node_type = node.get("node_type", "")
        node_id = node.get("node_id", "")
        if node_type not in node_types:
            node_types[node_type] = []
        node_types[node_type].append({"id": node_id, "node": node})

    log.log_info(f"[CONN] Node types found: {list(node_types.keys())}")

    # === EXECUTION FLOW CONNECTIONS ===
    # Start from function entry (always exists)
    current_exec_source = "entry"
    current_exec_pin = "then"

    # Define execution order based on common Blueprint patterns
    execution_order = [
        "RandomIntegerInRange", "RandomFloatInRange", "RandomInteger",
        "MakeLiteralString", "StringLength", "Append",
        "Add_FloatFloat", "Add_IntInt", "Multiply_FloatFloat", "Multiply_IntInt",
        "MakeVector", "BreakVector",
        "ForLoop", "Branch", "Delay",
        "PrintString",
        "K2Node_FunctionResult"
    ]

    # Connect nodes in logical execution order
    for node_type in execution_order:
        if node_type in node_types:
            for node_info in node_types[node_type]:
                node_id = node_info["id"]

                # Get the actual node GUID from mapping
                actual_node_guid = node_mapping.get(node_id)
                if not actual_node_guid:
                    log.log_warning(f"[CONN] No GUID found for node {node_id}")
                    continue

                # Determine input execution pin name
                exec_input_pin = "exec"
                if node_type in ["ForLoop", "Branch"]:
                    exec_input_pin = "exec"
                elif node_type == "Delay":
                    exec_input_pin = "exec"
                elif node_type == "PrintString":
                    exec_input_pin = "exec"
                elif node_type == "K2Node_FunctionResult":
                    exec_input_pin = "exec"

                # Create execution connection
                if current_exec_source != "entry" or node_type != "K2Node_FunctionResult":
                    connections.append({
                        "from_node": current_exec_source,
                        "from_pin": current_exec_pin,
                        "to_node": actual_node_guid,
                        "to_pin": exec_input_pin
                    })
                    log.log_info(f"[CONN] Execution: {current_exec_source}.{current_exec_pin} -> {node_id}.{exec_input_pin}")

                # Update for next connection
                current_exec_source = actual_node_guid
                current_exec_pin = "then"
                if node_type == "ForLoop":
                    current_exec_pin = "Completed"
                elif node_type == "Branch":
                    current_exec_pin = "True"  # Could also be "False" depending on logic

    # === DATA FLOW CONNECTIONS ===
    # Connect data outputs to inputs where logical

    # Random number to print string
    if "RandomIntegerInRange" in node_types and "PrintString" in node_types:
        random_node = node_types["RandomIntegerInRange"][0]
        print_node = node_types["PrintString"][0]

        random_guid = node_mapping.get(random_node["id"])
        print_guid = node_mapping.get(print_node["id"])

        if random_guid and print_guid:
            connections.append({
                "from_node": random_guid,
                "from_pin": "ReturnValue",
                "to_node": print_guid,
                "to_pin": "InString"
            })
            log.log_info(f"[CONN] Data: Random.ReturnValue -> Print.InString")

    # String operations chaining
    if "MakeLiteralString" in node_types and "Append" in node_types:
        literal_node = node_types["MakeLiteralString"][0]
        append_node = node_types["Append"][0]

        literal_guid = node_mapping.get(literal_node["id"])
        append_guid = node_mapping.get(append_node["id"])

        if literal_guid and append_guid:
            connections.append({
                "from_node": literal_guid,
                "from_pin": "ReturnValue",
                "to_node": append_guid,
                "to_pin": "A"
            })
            log.log_info(f"[CONN] Data: Literal.ReturnValue -> Append.A")

    # Math operations
    if "Add_FloatFloat" in node_types or "Add_IntInt" in node_types:
        add_type = "Add_FloatFloat" if "Add_FloatFloat" in node_types else "Add_IntInt"
        add_node = node_types[add_type][0]
        add_guid = node_mapping.get(add_node["id"])

        # Connect to print if available
        if "PrintString" in node_types and add_guid:
            print_node = node_types["PrintString"][0]
            print_guid = node_mapping.get(print_node["id"])

            if print_guid:
                connections.append({
                    "from_node": add_guid,
                    "from_pin": "ReturnValue",
                    "to_node": print_guid,
                    "to_pin": "InString"
                })
                log.log_info(f"[CONN] Data: Add.ReturnValue -> Print.InString")

    # Connect to return node if outputs are specified
    if outputs and "K2Node_FunctionResult" in node_types:
        return_node = node_types["K2Node_FunctionResult"][0]
        return_guid = node_mapping.get(return_node["id"])

        if return_guid:
            # Find a suitable data source for the return value
            data_source_guid = None
            data_source_pin = "ReturnValue"

            for node_type in ["RandomIntegerInRange", "Add_FloatFloat", "Add_IntInt", "Append"]:
                if node_type in node_types:
                    source_node = node_types[node_type][0]
                    data_source_guid = node_mapping.get(source_node["id"])
                    break

            if data_source_guid and outputs:
                output_pin_name = outputs[0].get("name", "ReturnValue")
                connections.append({
                    "from_node": data_source_guid,
                    "from_pin": data_source_pin,
                    "to_node": return_guid,
                    "to_pin": output_pin_name
                })
                log.log_info(f"[CONN] Data: Source.{data_source_pin} -> Return.{output_pin_name}")

    log.log_info(f"[CONN] Generated {len(connections)} connections")
    return connections


def _generate_function_name_from_description(description: str) -> str:
    """
    Generate a function name from the description - LOCAL UTILITY
    """
    import re
    
    # Extract key words and convert to PascalCase
    words = re.findall(r'\b\w+\b', description.lower())
    
    # Remove common words
    stop_words = {'a', 'an', 'the', 'and', 'or', 'but', 'for', 'to', 'of', 'in', 'on', 'at', 'by', 'with', 'that', 'this', 'is', 'are', 'was', 'will', 'be'}
    meaningful_words = [word for word in words if word not in stop_words and len(word) > 2]
    
    # Take first 3-4 meaningful words and capitalize
    function_words = meaningful_words[:4]
    function_name = ''.join(word.capitalize() for word in function_words)
    
    # Ensure it starts with uppercase and has reasonable length
    if len(function_name) < 3:
        function_name = "GeneratedFunction"
    elif len(function_name) > 50:
        function_name = function_name[:50]
    
    return function_name


def _analyze_description_for_parameters(description: str) -> Tuple[List[Dict], List[Dict]]:
    """
    Analyze function description to determine appropriate input/output parameters - LOCAL AI LOGIC
    """
    desc_lower = description.lower()
    inputs = []
    outputs = []
    
    # Analyze for common patterns
    if "password" in desc_lower:
        inputs = [
            {"name": "PasswordLength", "type": "int32"},
            {"name": "IncludeUppercase", "type": "bool"},
            {"name": "IncludeLowercase", "type": "bool"},
            {"name": "IncludeNumbers", "type": "bool"},
            {"name": "IncludeSpecialChars", "type": "bool"}
        ]
        outputs = [
            {"name": "GeneratedPassword", "type": "string"},
            {"name": "IsValid", "type": "bool"},
            {"name": "ErrorMessage", "type": "string"}
        ]
    elif "calculate" in desc_lower or "math" in desc_lower:
        inputs = [
            {"name": "ValueA", "type": "float"},
            {"name": "ValueB", "type": "float"}
        ]
        outputs = [
            {"name": "Result", "type": "float"},
            {"name": "IsValid", "type": "bool"}
        ]
    elif "string" in desc_lower or "text" in desc_lower:
        inputs = [
            {"name": "InputText", "type": "string"}
        ]
        outputs = [
            {"name": "OutputText", "type": "string"}
        ]
    elif "number" in desc_lower or "count" in desc_lower:
        inputs = [
            {"name": "InputValue", "type": "int32"}
        ]
        outputs = [
            {"name": "OutputValue", "type": "int32"}
        ]
    else:
        # Generic parameters
        outputs = [
            {"name": "Result", "type": "string"},
            {"name": "Success", "type": "bool"}
        ]
    
    return inputs, outputs

# --- DYNAMIC NODE LOOKUP UTILITY ---
def find_blueprint_node(node_purpose, description=None):
    """
    Dynamically look up the correct Blueprint node type, function name, and class for a given purpose.
    For now, uses a static registry, but can be extended to query Unreal's Python API for all available nodes.
    Args:
        node_purpose: e.g. 'random_integer', 'print_string', 'set_variable'
        description: Optional, for more context
    Returns:
        Dict with node_type, function_name, function_class, etc.
    """
    # TODO: Replace this with a dynamic query to Unreal's Blueprint libraries via Python API
    registry = {
        'random_integer': {
            'node_type': 'K2Node_CallFunction',
            'function_name': 'RandomIntegerInRange',
            'function_class': 'KismetMathLibrary',
        },
        'print_string': {
            'node_type': 'K2Node_CallFunction',
            'function_name': 'PrintString',
            'function_class': 'KismetSystemLibrary',
        },
        'set_variable': {
            'node_type': 'K2Node_VariableSet',
        },
    }
    return registry.get(node_purpose, {})

# New approach: Create complete Blueprint graphs with connected nodes
class BlueprintGraphCreator:
    def __init__(self):
        self.node_registry = {}
        self.connection_registry = []
        
    def create_password_generator_blueprint(self, blueprint_path=None):
        """
        Create a complete password generator Blueprint using the unreal-mcp approach
        - Create all nodes at once
        - Connect them properly
        - Ensure proper pin connections
        """
        try:
            # If no path provided, create new Blueprint
            if not blueprint_path:
                blueprint_path = '/Game/GeneratedBlueprints/PasswordGenerator'
            
            print(f"Creating password generator Blueprint at: {blueprint_path}")
            
            # Create the Blueprint asset
            blueprint = self._create_blueprint_asset(blueprint_path)
            if not blueprint:
                return None
                
            # Create a function in the Blueprint
            function_graph = self._create_function(blueprint, "GeneratePassword")
            if not function_graph:
                return None
                
            # Create the complete node graph using batch creation
            success = self._create_password_generator_graph(function_graph)
            
            if success:
                # Compile and save the Blueprint
                unreal.BlueprintEditorUtils.compile_blueprint(blueprint)
                unreal.EditorAssetLibrary.save_asset(blueprint_path, False)
                print(f"Successfully created password generator Blueprint: {blueprint_path}")
                return blueprint_path
            else:
                print("Failed to create password generator graph")
                return None
                
        except Exception as e:
            print(f"Error creating password generator Blueprint: {str(e)}")
            return None
    
    def _create_blueprint_asset(self, blueprint_path):
        """Create a new Blueprint asset"""
        try:
            # Create Blueprint factory
            blueprint_factory = unreal.BlueprintFactory()
            blueprint_factory.parent_class = unreal.Actor
            
            # Create the asset
            asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
            package_path = blueprint_path.rsplit('/', 1)[0]
            asset_name = blueprint_path.rsplit('/', 1)[1]
            
            blueprint = asset_tools.create_asset(
                asset_name,
                package_path,
                unreal.Blueprint,
                blueprint_factory
            )
            
            return blueprint
            
        except Exception as e:
            print(f"Error creating Blueprint asset: {str(e)}")
            return None
    
    def _create_function(self, blueprint, function_name):
        """Create a new function in the Blueprint"""
        try:
            # Create function graph
            function_graph = unreal.BlueprintEditorUtils.create_new_graph(
                blueprint,
                function_name,
                unreal.EdGraph,
                unreal.EdGraphSchema_K2
            )
            
            if function_graph:
                # Add to function graphs
                blueprint.function_graphs.append(function_graph)
                
                # Create function entry node
                entry_node = unreal.EdGraphNode()
                entry_node.node_pos_x = 0
                entry_node.node_pos_y = 0
                function_graph.add_node(entry_node, True, True)
                
                print(f"Created function: {function_name}")
                return function_graph
            
        except Exception as e:
            print(f"Error creating function: {str(e)}")
            
        return None
    
    def _create_password_generator_graph(self, function_graph):
        """Create the complete password generator node graph with connections"""
        try:
            # Define the complete node structure for password generation
            nodes_data = [
                {
                    "id": "entry",
                    "type": "FunctionEntry",
                    "pos": [0, 0],
                    "outputs": [{"name": "exec", "type": "exec"}]
                },
                {
                    "id": "make_password_chars",
                    "type": "MakeLiteralString", 
                    "pos": [200, -100],
                    "properties": {"value": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"},
                    "outputs": [{"name": "string", "type": "string"}]
                },
                {
                    "id": "make_empty_result",
                    "type": "MakeLiteralString",
                    "pos": [200, 0], 
                    "properties": {"value": ""},
                    "outputs": [{"name": "string", "type": "string"}]
                },
                {
                    "id": "string_length",
                    "type": "StringLength",
                    "pos": [400, -100],
                    "inputs": [{"name": "string", "type": "string"}],
                    "outputs": [{"name": "length", "type": "int"}]
                },
                {
                    "id": "for_loop",
                    "type": "ForLoop", 
                    "pos": [600, 0],
                    "inputs": [
                        {"name": "exec", "type": "exec"},
                        {"name": "first_index", "type": "int"},
                        {"name": "last_index", "type": "int"}
                    ],
                    "outputs": [
                        {"name": "exec", "type": "exec"},
                        {"name": "loop_body", "type": "exec"},
                        {"name": "index", "type": "int"},
                        {"name": "completed", "type": "exec"}
                    ]
                },
                {
                    "id": "random_int",
                    "type": "RandomIntegerInRange",
                    "pos": [800, 100],
                    "inputs": [
                        {"name": "min", "type": "int"},
                        {"name": "max", "type": "int"}
                    ],
                    "outputs": [{"name": "value", "type": "int"}]
                },
                {
                    "id": "get_char",
                    "type": "GetCharacterAtIndex",
                    "pos": [1000, 0],
                    "inputs": [
                        {"name": "string", "type": "string"},
                        {"name": "index", "type": "int"}
                    ],
                    "outputs": [{"name": "char", "type": "string"}]
                },
                {
                    "id": "append_char",
                    "type": "Append",
                    "pos": [1200, 0],
                    "inputs": [
                        {"name": "a", "type": "string"},
                        {"name": "b", "type": "string"}
                    ],
                    "outputs": [{"name": "result", "type": "string"}]
                },
                {
                    "id": "print_result",
                    "type": "PrintString",
                    "pos": [800, -200],
                    "inputs": [
                        {"name": "exec", "type": "exec"},
                        {"name": "string", "type": "string"}
                    ],
                    "outputs": [{"name": "exec", "type": "exec"}]
                },
                {
                    "id": "return_node",
                    "type": "FunctionResult",
                    "pos": [1000, -200],
                    "inputs": [{"name": "exec", "type": "exec"}]
                }
            ]
            
            # Define connections between nodes
            connections = [
                {"from": "entry", "from_pin": "exec", "to": "for_loop", "to_pin": "exec"},
                {"from": "make_password_chars", "from_pin": "string", "to": "string_length", "to_pin": "string"},
                {"from": "make_password_chars", "from_pin": "string", "to": "get_char", "to_pin": "string"},
                {"from": "string_length", "from_pin": "length", "to": "random_int", "to_pin": "max"},
                {"from": "for_loop", "from_pin": "loop_body", "to": "random_int", "to_pin": "exec"},
                {"from": "random_int", "from_pin": "value", "to": "get_char", "to_pin": "index"},
                {"from": "get_char", "from_pin": "char", "to": "append_char", "to_pin": "b"},
                {"from": "make_empty_result", "from_pin": "string", "to": "append_char", "to_pin": "a"},
                {"from": "for_loop", "from_pin": "completed", "to": "print_result", "to_pin": "exec"},
                {"from": "append_char", "from_pin": "result", "to": "print_result", "to_pin": "string"},
                {"from": "print_result", "from_pin": "exec", "to": "return_node", "to_pin": "exec"}
            ]
            
            # Create all nodes using bulk creation
            created_nodes = self._create_nodes_bulk(function_graph, nodes_data)
            if not created_nodes:
                return False
            
            # Connect all nodes
            success = self._connect_nodes_bulk(function_graph, connections, created_nodes)
            
            return success
            
        except Exception as e:
            print(f"Error creating password generator graph: {str(e)}")
            return False
    
    def _create_nodes_bulk(self, function_graph, nodes_data):
        """Create all nodes at once using the bulk creation approach"""
        try:
            # Prepare bulk creation data
            blueprint_path = function_graph.get_outer().get_path_name()
            function_guid = str(function_graph.graph_guid)
            
            # Convert our node data to the format expected by the C++ system
            bulk_nodes = []
            for node_data in nodes_data:
                bulk_node = {
                    "id": node_data["id"],
                    "node_type": node_data["type"],
                    "node_position": node_data["pos"],
                    "node_properties": node_data.get("properties", {})
                }
                bulk_nodes.append(bulk_node)
            
            # Call the C++ bulk creation method
            nodes_json = json.dumps(bulk_nodes)
            
            # Use the existing C++ implementation but with our improved approach
            result = unreal.GenBlueprintNodeCreator.add_nodes_bulk(
                blueprint_path,
                function_guid, 
                nodes_json
            )
            
            if result:
                # Parse the result to get node mappings
                created_nodes = {}
                try:
                    result_data = json.loads(result)
                    for item in result_data:
                        if "ref_id" in item and "node_guid" in item:
                            created_nodes[item["ref_id"]] = item["node_guid"]
                except:
                    print("Failed to parse bulk creation result")
                    return None
                
                print(f"Successfully created {len(created_nodes)} nodes")
                return created_nodes
            else:
                print("Bulk node creation failed")
                return None
                
        except Exception as e:
            print(f"Error in bulk node creation: {str(e)}")
            return None
    
    def _connect_nodes_bulk(self, function_graph, connections, created_nodes):
        """Connect all nodes using a bulk connection approach"""
        try:
            blueprint_path = function_graph.get_outer().get_path_name()
            function_guid = str(function_graph.graph_guid)
            
            # Convert connections to the format expected by C++
            connection_data = []
            for conn in connections:
                from_guid = created_nodes.get(conn["from"])
                to_guid = created_nodes.get(conn["to"])
                
                if from_guid and to_guid:
                    connection_data.append({
                        "from_node": from_guid,
                        "from_pin": conn["from_pin"],
                        "to_node": to_guid,
                        "to_pin": conn["to_pin"]
                    })
            
            # Use the new C++ bulk connection method
            connections_json = json.dumps(connection_data)
            result = unreal.GenBlueprintNodeCreator.connect_nodes_bulk(
                blueprint_path,
                function_guid,
                connections_json
            )
            
            if result:
                try:
                    result_data = json.loads(result)
                    success = result_data.get("success", False)
                    successful_connections = result_data.get("successful_connections", 0)
                    total_connections = result_data.get("total_connections", 0)
                    
                    print(f"Connected {successful_connections} out of {total_connections} connections")
                    return success
                except:
                    print("Failed to parse connection result")
                    return False
            else:
                print("Bulk connection failed")
                return False
            
        except Exception as e:
            print(f"Error connecting nodes: {str(e)}")
            return False

    def create_complete_password_generator(self, blueprint_path=None):
        """Create complete password generator using the new CreateCompleteGraph method"""
        try:
            # If no path provided, create new Blueprint
            if not blueprint_path:
                blueprint_path = '/Game/GeneratedBlueprints/PasswordGeneratorComplete'
            
            print(f"Creating complete password generator Blueprint at: {blueprint_path}")
            
            # Create the Blueprint asset
            blueprint = self._create_blueprint_asset(blueprint_path)
            if not blueprint:
                return None
                
            # Create a function in the Blueprint
            function_graph = self._create_function(blueprint, "GeneratePassword")
            if not function_graph:
                return None
            
            # Define the complete graph structure
            graph_definition = {
                "nodes": [
                    {
                        "id": "entry",
                        "node_type": "FunctionEntry",
                        "node_position": [0, 0],
                        "node_properties": {}
                    },
                    {
                        "id": "make_password_chars",
                        "node_type": "MakeLiteralString", 
                        "node_position": [200, -100],
                        "node_properties": {"string_value": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"}
                    },
                    {
                        "id": "make_empty_result",
                        "node_type": "MakeLiteralString",
                        "node_position": [200, 0], 
                        "node_properties": {"string_value": ""}
                    },
                    {
                        "id": "string_length",
                        "node_type": "StringLength",
                        "node_position": [400, -100],
                        "node_properties": {}
                    },
                    {
                        "id": "for_loop",
                        "node_type": "ForLoop", 
                        "node_position": [600, 0],
                        "node_properties": {}
                    },
                    {
                        "id": "random_int",
                        "node_type": "RandomIntegerInRange",
                        "node_position": [800, 100],
                        "node_properties": {}
                    },
                    {
                        "id": "get_char",
                        "node_type": "GetCharacterAtIndex",
                        "node_position": [1000, 0],
                        "node_properties": {}
                    },
                    {
                        "id": "append_char",
                        "node_type": "Append",
                        "node_position": [1200, 0],
                        "node_properties": {}
                    },
                    {
                        "id": "print_result",
                        "node_type": "PrintString",
                        "node_position": [800, -200],
                        "node_properties": {}
                    },
                    {
                        "id": "return_node",
                        "node_type": "FunctionResult",
                        "node_position": [1000, -200],
                        "node_properties": {}
                    }
                ],
                "connections": [
                    {"from": "entry", "from_pin": "exec", "to": "for_loop", "to_pin": "exec"},
                    {"from": "make_password_chars", "from_pin": "string", "to": "string_length", "to_pin": "string"},
                    {"from": "make_password_chars", "from_pin": "string", "to": "get_char", "to_pin": "string"},
                    {"from": "string_length", "from_pin": "length", "to": "random_int", "to_pin": "max"},
                    {"from": "for_loop", "from_pin": "loop_body", "to": "random_int", "to_pin": "exec"},
                    {"from": "random_int", "from_pin": "value", "to": "get_char", "to_pin": "index"},
                    {"from": "get_char", "from_pin": "char", "to": "append_char", "to_pin": "b"},
                    {"from": "make_empty_result", "from_pin": "string", "to": "append_char", "to_pin": "a"},
                    {"from": "for_loop", "from_pin": "completed", "to": "print_result", "to_pin": "exec"},
                    {"from": "append_char", "from_pin": "result", "to": "print_result", "to_pin": "string"},
                    {"from": "print_result", "from_pin": "exec", "to": "return_node", "to_pin": "exec"}
                ]
            }
            
            # Use the new CreateCompleteGraph method
            graph_json = json.dumps(graph_definition)
            result = unreal.GenBlueprintNodeCreator.create_complete_graph(
                blueprint_path,
                str(function_graph.graph_guid),
                graph_json
            )
            
            if result:
                try:
                    result_data = json.loads(result)
                    success = result_data.get("success", False)
                    
                    if success:
                        # Compile and save the Blueprint
                        unreal.BlueprintEditorUtils.compile_blueprint(blueprint)
                        unreal.EditorAssetLibrary.save_asset(blueprint_path, False)
                        
                        print(f"Successfully created complete password generator Blueprint: {blueprint_path}")
                        print(f"Result: {result}")
                        return blueprint_path
                    else:
                        print("Failed to create complete graph")
                        print(f"Result: {result}")
                        return None
                except:
                    print("Failed to parse complete graph result")
                    return None
            else:
                print("CreateCompleteGraph returned empty result")
                return None
                
        except Exception as e:
            print(f"Error creating complete password generator Blueprint: {str(e)}")
            return None

# Global instance
blueprint_creator = BlueprintGraphCreator()

def create_password_generator():
    """Create a complete password generator Blueprint using original approach"""
    return blueprint_creator.create_password_generator_blueprint()

def create_complete_password_generator():
    """Create a complete password generator Blueprint using new CreateCompleteGraph approach"""
    return blueprint_creator.create_complete_password_generator()

def create_blueprint_function(blueprint_name, function_name, parameters=None):
    """Create a Blueprint function with the given name and parameters"""
    try:
        # Get or create blueprint
        blueprint_path = f'/Game/GeneratedBlueprints/{blueprint_name}'
        
        # Use existing Blueprint loading logic from unreal
        existing_blueprint = unreal.EditorAssetLibrary.find_asset_data(blueprint_path)
        
        if existing_blueprint.is_valid():
            blueprint = unreal.EditorAssetLibrary.load_asset(blueprint_path)
        else:
            blueprint = blueprint_creator._create_blueprint_asset(blueprint_path)
            
        if not blueprint:
            return None
            
        # Create the function
        function_graph = blueprint_creator._create_function(blueprint, function_name)
        
        if function_graph:
            # Compile and save
            unreal.BlueprintEditorUtils.compile_blueprint(blueprint)
            unreal.EditorAssetLibrary.save_asset(blueprint_path, False)
            
            return {
                'blueprint_path': blueprint_path,
                'function_guid': str(function_graph.graph_guid),
                'function_name': function_name
            }
        
        return None
        
    except Exception as e:
        print(f"Error creating Blueprint function: {str(e)}")
        return None

def handle_get_unreal_context(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to get current Unreal Engine context data - LOCAL IMPLEMENTATION
    Uses the C++ sync system to get current Blueprint, window, and asset context
    
    Args:
        command: The command dictionary (no specific arguments needed)
            
    Returns:
        Response dictionary with current UE context data
    """
    try:
        log.log_command("get_unreal_context", "Getting current Unreal Engine context")
        
        # Import the blueprint context handler to use the C++ sync system
        try:
            import blueprint_context_handler
            log.log_info("✅ Successfully imported blueprint_context_handler")
        except ImportError as e:
            log.log_error(f"Failed to import blueprint_context_handler: {e}")
            return {
                "success": False,
                "error": f"Could not import blueprint context handler: {str(e)}",
                "suggestion": "Ensure the blueprint_context_handler.py file is in the handlers directory"
            }
        
        # Get current Blueprint context using the C++ sync system
        try:
            context_result = blueprint_context_handler.handle_blueprint_context_request(
                "get_current_blueprint_context", {}
            )
            
            if context_result.get("success", False):
                log.log_info("✅ Successfully retrieved UE context via C++ sync")
                
                # Extract the context data
                context_data = context_result.get("context", {})
                
                # Get additional UE editor information
                editor_info = _get_editor_context_info()
                
                # Combine all context information
                full_context = {
                    "blueprint_context": context_data,
                    "editor_context": editor_info,
                    "timestamp": time.time(),
                    "sync_method": "cpp_asset_registry"
                }
                
                log.log_result("get_unreal_context", True, f"Retrieved context with {len(context_data.get('blueprints', []))} blueprints")
                return {
                    "success": True,
                    "message": "Current Unreal Engine context retrieved successfully",
                    "context": full_context,
                    "blueprint_count": len(context_data.get("blueprints", [])),
                    "current_blueprint": context_data.get("current_blueprint", {}),
                    "editor_info": editor_info
                }
            else:
                log.log_error(f"Failed to get Blueprint context: {context_result}")
                return {
                    "success": False,
                    "error": f"Blueprint context retrieval failed: {context_result.get('error', 'Unknown error')}",
                    "context_result": context_result
                }
                
        except Exception as e:
            log.log_error(f"Exception during context retrieval: {str(e)}")
            return {
                "success": False,
                "error": f"Exception getting UE context: {str(e)}"
            }
            
    except Exception as e:
        log.log_error(f"Error getting Unreal Engine context: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def _get_editor_context_info() -> Dict[str, Any]:
    """
    Get additional Unreal Editor context information
    """
    try:
        editor_info = {
            "current_level": None,
            "current_world": None,
            "open_assets": [],
            "editor_mode": "Unknown",
            "project_path": None
        }
        
        # Get current level
        try:
            current_level = unreal.EditorLevelLibrary.get_current_level()
            if current_level:
                editor_info["current_level"] = {
                    "name": current_level.get_name(),
                    "path": current_level.get_path_name(),
                    "actor_count": len(unreal.EditorLevelLibrary.get_all_level_actors())
                }
        except Exception as e:
            log.log_warning(f"Could not get current level: {e}")
        
        # Get current world
        try:
            current_world = unreal.EditorLevelLibrary.get_editor_world()
            if current_world:
                editor_info["current_world"] = {
                    "name": current_world.get_name(),
                    "path": current_world.get_path_name(),
                    "level_count": len(current_world.get_levels())
                }
        except Exception as e:
            log.log_warning(f"Could not get current world: {e}")
        
        # Get project path
        try:
            editor_info["project_path"] = unreal.Paths.project_dir()
        except Exception as e:
            log.log_warning(f"Could not get project path: {e}")
        
        # Try to get open assets (if available)
        try:
            asset_subsystem = unreal.get_editor_subsystem(unreal.AssetEditorSubsystem)
            if asset_subsystem and hasattr(asset_subsystem, "get_all_edited_assets"):
                edited_assets = asset_subsystem.get_all_edited_assets()
                for asset in edited_assets:
                    if asset:
                        editor_info["open_assets"].append({
                            "name": asset.get_name(),
                            "path": asset.get_path_name(),
                            "type": type(asset).__name__
                        })
        except Exception as e:
            log.log_warning(f"Could not get open assets: {e}")
        
        return editor_info
        
    except Exception as e:
        log.log_error(f"Error getting editor context: {e}")
        return {"error": str(e)}

def handle_analyze_graph(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze a Blueprint function graph and return all nodes and connections.
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function graph
    Returns:
        Dictionary with nodes, pins, and connections
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        if not blueprint_path or not function_id:
            log.log_error("Missing required parameters for analyze_graph")
            return {"success": False, "error": "Missing required parameters"}
        node_creator = unreal.GenBlueprintNodeCreator
        analysis_json = node_creator.analyze_graph(blueprint_path, function_id)
        import json
        analysis = json.loads(analysis_json)
        return {"success": True, "analysis": analysis}
    except Exception as e:
        log.log_error(f"Error analyzing graph: {str(e)}")
        return {"success": False, "error": str(e)}

def handle_get_focused_graph(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get the currently focused graph/tab in the Blueprint Editor.
    Returns:
        Dictionary with graph name, GUID, blueprint name, and path, or error info.
    """
    try:
        node_creator = unreal.GenBlueprintNodeCreator
        focused_json = node_creator.get_current_focused_graph()
        import json
        focused_info = json.loads(focused_json)
        return {"success": True, "focused_graph": focused_info}
    except Exception as e:
        log.log_error(f"Error getting focused graph: {str(e)}")
        return {"success": False, "error": str(e)}