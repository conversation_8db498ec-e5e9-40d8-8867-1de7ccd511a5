#!/usr/bin/env python3
"""
Test script for YourLife project with FGraphNodeCreator fix
"""

import socket
import json
import time

def test_yourlife_fgraphnode_fix():
    """Test the FGraphNodeCreator fix with YourLife project"""
    print("🚨🚨🚨 TESTING FGRAPHNODE_CREATOR FIX WITH YOURLIFE PROJECT")
    print("=" * 60)
    print("🎯 CRITICAL: This test validates the C++ FGraphNodeCreator pattern fix")
    print("📁 Project: C:\\Dev\\YourLife")
    print("🔧 Plugin: C:\\Dev\\YourLife\\Plugins\\CreatelexGenAI")
    print()
    print("🔧 This test uses the FIXED C++ implementation with:")
    print("   - FGraphNodeCreator<UK2Node_CallFunction> pattern")
    print("   - Proper Creator.Finalize() call")
    print("   - Exception handling")
    print("   - IMMEDIATE RETURN TEST (no actual node creation)")
    print()
    
    # Connect to Unreal Engine socket server
    try:
        sock = socket.socket(socket.AF_INET6, socket.SOCK_STREAM)
        sock.connect(('::1', 9877))
        print("✅ Connected to Unreal Engine socket server")
    except Exception as e:
        print(f"❌ Failed to connect to socket server: {e}")
        print("💡 Make sure:")
        print("   1. Unreal Engine is running")
        print("   2. YourLife project is loaded")
        print("   3. CreatelexGenAI plugin is enabled")
        print("   4. Socket server is started (port 9877)")
        return False
    
    try:
        # Test: Immediate return test to validate C++ function call
        print("\n🧪 TEST: C++ Function Call Validation (Immediate Return)")
        print("🎯 Goal: Confirm C++ AddNodesBulk function is called without crashing")
        
        command = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/ThirdPerson/Blueprints/BP_ThirdPersonCharacter.BP_ThirdPersonCharacter",
            "function_id": "EventGraph",  # Use EventGraph for testing
            "nodes": [
                {
                    "id": "test_node",
                    "node_type": "PrintString",
                    "node_position": [100, 100],
                    "node_properties": {}
                }
            ]
        }
        
        # Send command
        command_json = json.dumps(command)
        sock.send(command_json.encode('utf-8'))
        print(f"📤 Sent command: {command_json}")
        
        # Receive response
        print("⏳ Waiting for response...")
        response_data = sock.recv(4096)
        response_str = response_data.decode('utf-8')
        print(f"📥 Received response: {response_str}")
        
        # Parse response
        try:
            response = json.loads(response_str)
            
            # Check for success
            if response.get("success"):
                print("✅ SUCCESS: C++ function called successfully!")
                print("🎉 FGraphNodeCreator fix is working!")
                
                # Check for expected message
                if "C++ function called successfully - no crash!" in response.get("message", ""):
                    print("✅ CONFIRMED: Immediate return test working as expected")
                    print("🚨🚨🚨 BREAKTHROUGH: C++ AddNodesBulk function is now callable!")
                    return True
                else:
                    print("⚠️  SUCCESS but unexpected message format")
                    return True
            else:
                print("❌ FAILED: C++ function returned error")
                print(f"   Error: {response.get('error', 'Unknown error')}")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse JSON response: {e}")
            print(f"   Raw response: {response_str}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    finally:
        sock.close()
        print("🔌 Socket connection closed")

def main():
    """Main test function"""
    print("🚨🚨🚨 YOURLIFE PROJECT - FGRAPHNODE_CREATOR FIX TEST")
    print("=" * 70)
    print()
    
    success = test_yourlife_fgraphnode_fix()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 TEST PASSED: FGraphNodeCreator fix is working!")
        print("✅ Next steps:")
        print("   1. Remove immediate return test")
        print("   2. Enable full node creation logic")
        print("   3. Test actual node creation")
        print("   4. Test pin connections")
    else:
        print("❌ TEST FAILED: FGraphNodeCreator fix needs more work")
        print("🔧 Debug steps:")
        print("   1. Check Unreal Engine logs for 🚨🚨🚨 messages")
        print("   2. Verify YourLife project is loaded")
        print("   3. Confirm plugin compilation")
        print("   4. Check socket server status")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
