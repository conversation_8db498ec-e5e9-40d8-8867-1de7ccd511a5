#!/usr/bin/env python3
"""
Minimal test to isolate the Blueprint function generation hang.
"""

import json
import socket
import time

def test_minimal_blueprint():
    """Test minimal Blueprint function generation."""
    
    print("🧪 Testing Minimal Blueprint Function Generation")
    print("=" * 50)
    
    try:
        # Connect to Unreal Engine
        print("🔌 Connecting to Unreal Engine...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(60)  # Longer timeout
        sock.connect(('127.0.0.1', 9877))
        print("✅ Connected!")
        
        # Send a very minimal request
        minimal_request = {
            "type": "generate_smart_blueprint_function",
            "function_name": "MinimalTest",
            "description": "Simple test",
            "inputs": [],
            "outputs": [],
            "complexity": "simple",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "use_current_context": False
        }
        
        print("📤 Sending minimal request...")
        print(f"📋 Request: {json.dumps(minimal_request, indent=2)}")
        
        request_json = json.dumps(minimal_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        print("📥 Waiting for response (60s timeout)...")
        start_time = time.time()
        
        # Try to receive response
        response_data = sock.recv(8192).decode('utf-8')
        elapsed = time.time() - start_time
        
        sock.close()
        
        print(f"✅ Response received in {elapsed:.2f}s!")
        print(f"📊 Response length: {len(response_data)} characters")
        
        # Try to parse as JSON
        try:
            response = json.loads(response_data)
            print(f"📊 Success: {response.get('success', 'Unknown')}")
            if 'connections_made' in response:
                print(f"🔗 Connections made: {response['connections_made']}")
            if 'nodes_created' in response:
                print(f"🔧 Nodes created: {response['nodes_created']}")
            if 'error' in response:
                print(f"❌ Error: {response['error']}")
            if 'message' in response:
                print(f"💬 Message: {response['message']}")
            return True
        except json.JSONDecodeError:
            print("⚠️ Response is not JSON:")
            print(response_data[:500] + "..." if len(response_data) > 500 else response_data)
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_minimal_blueprint()
