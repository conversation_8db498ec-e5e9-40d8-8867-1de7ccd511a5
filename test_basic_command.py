#!/usr/bin/env python3
"""
Test basic commands that should work quickly.
"""

import json
import socket
import time

def test_basic_command():
    """Test basic commands to see if socket server is responsive."""
    
    print("🧪 Testing Basic Commands")
    print("=" * 30)
    
    # Test commands that should respond quickly
    test_commands = [
        {
            "name": "spawn_object",
            "command": {
                "type": "spawn_object",
                "object_type": "Cube",
                "location": [0, 0, 100]
            }
        },
        {
            "name": "get_unreal_context", 
            "command": {
                "type": "get_unreal_context"
            }
        }
    ]
    
    for test in test_commands:
        try:
            print(f"\n🔌 Testing {test['name']}...")
            sock = socket.socket(socket.AF_INET6, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect(('::1', 9877))
            
            request_json = json.dumps(test['command']) + '\n'
            sock.send(request_json.encode('utf-8'))
            
            print("📥 Waiting for response...")
            response_data = sock.recv(4096).decode('utf-8')
            sock.close()
            
            print(f"✅ {test['name']} responded!")
            print(f"📊 Response: {response_data[:100]}...")
            
        except Exception as e:
            print(f"❌ {test['name']} failed: {e}")

if __name__ == "__main__":
    test_basic_command()
