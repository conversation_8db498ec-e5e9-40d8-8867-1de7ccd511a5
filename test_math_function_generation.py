#!/usr/bin/env python3
"""
Test script to verify that our Add_FloatFloat fixes are working correctly.
This script will test the Blueprint function generation with mathematical operations.
"""

import json
import socket
import time

def test_math_function_generation():
    """Test creating a Blueprint function with mathematical operations."""
    
    # Test data for creating a function that adds two float numbers
    # Use the Level Script Blueprint directly
    test_request = {
        "type": "generate_smart_blueprint_function",
        "function_name": "AddTwoNumbers",
        "description": "Create a function that takes two float inputs and returns their sum using Add_FloatFloat",
        "inputs": [
            {"name": "FirstNumber", "type": "float", "description": "First number to add"},
            {"name": "SecondNumber", "type": "float", "description": "Second number to add"}
        ],
        "outputs": [
            {"name": "Sum", "type": "float", "description": "The sum of the two numbers"}
        ],
        "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson"  # Direct path
    }
    
    try:
        # Connect to the Unreal Engine socket server
        print("🔌 Testing connection to Unreal Engine...")
        sock = None

        # Try IPv4 (127.0.0.1) - socket server is bound to IPv4
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect(('127.0.0.1', 9877))
            print("✅ Connected via IPv4 (127.0.0.1)")
        except Exception as e:
            print(f"❌ IPv4 connection failed: {e}")
            return False


        
        # Send the test request
        print("📤 Sending test request for math function generation...")
        request_json = json.dumps(test_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        # Receive the response
        print("📥 Waiting for response...")
        sock.settimeout(30)  # 30 second timeout
        response_data = sock.recv(4096).decode('utf-8')
        sock.close()
        
        print("✅ Response received:")
        print(response_data)
        
        # Parse and analyze the response
        try:
            response = json.loads(response_data)
            if response.get('success'):
                print("🎉 SUCCESS: Math function generation completed successfully!")
                print(f"📋 Details: {response.get('message', 'No details provided')}")
                return True
            else:
                print("❌ FAILED: Math function generation failed")
                print(f"📋 Error: {response.get('error', 'No error details provided')}")
                return False
        except json.JSONDecodeError:
            print("⚠️  Response is not valid JSON, but function may have succeeded")
            print(f"Raw response: {response_data}")
            return True
            
    except Exception as e:
        print(f"❌ ERROR: Failed to test math function generation: {e}")
        return False

def test_connection():
    """Test basic connection to Unreal Engine."""
    try:
        print("🔌 Testing connection to Unreal Engine...")
        sock = None

        # Try IPv6 first (::1)
        try:
            sock = socket.socket(socket.AF_INET6, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect(('::1', 9877))
            sock.close()
            print("✅ Connection successful via IPv6!")
            return True
        except Exception as e:
            print(f"❌ IPv6 connection failed: {e}")
            if sock:
                sock.close()

            # Try IPv4 (127.0.0.1)
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                sock.connect(('127.0.0.1', 9877))
                sock.close()
                print("✅ Connection successful via IPv4!")
                return True
            except Exception as e2:
                print(f"❌ IPv4 connection failed: {e2}")
                if sock:
                    sock.close()
                return False

    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing CreateLex Math Function Generation Fixes")
    print("=" * 60)
    
    # Test connection first
    if not test_connection():
        print("❌ Cannot connect to Unreal Engine. Make sure:")
        print("   1. Unreal Engine is running")
        print("   2. CreateLex plugin is loaded")
        print("   3. Socket server is running on port 9877")
        exit(1)
    
    print()
    
    # Test math function generation
    success = test_math_function_generation()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 TEST PASSED: Math function generation is working!")
        print("✅ Add_FloatFloat remapping fix appears to be successful")
    else:
        print("❌ TEST FAILED: Math function generation has issues")
        print("🔧 Check Unreal Engine logs for detailed error information")
    
    print("\n📋 Next steps:")
    print("   1. Check Unreal Engine logs for 'Remapping Add_FloatFloat' messages")
    print("   2. Verify that Blueprint function was created in /Game/TestBlueprints/")
    print("   3. Open the Blueprint and check if math nodes are properly connected")
