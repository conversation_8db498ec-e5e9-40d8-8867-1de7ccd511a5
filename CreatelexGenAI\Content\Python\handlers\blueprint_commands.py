import unreal
import json
from typing import Dict, Any, <PERSON>, Tuple, Union, Optional
import time
from difflib import get_close_matches

from utils import unreal_conversions as uc
from utils import logging as log

"""
NODE_PROPERTIES DOCUMENTATION
============================

The `node_properties` parameter allows you to set default values for node pins when creating nodes.
This is essential for configuring nodes with specific values without manual pin connection.

SUPPORTED PROPERTY TYPES:
- String: Text values (e.g., "Hello World", "MyVariable")
- Number: Numeric values (e.g., 42, 3.14, -1.5)
- Boolean: True/false values (e.g., true, false)

USAGE PATTERNS:
1. Pin Name Mapping: Properties are mapped to pins by name
2. Type Conversion: Values are automatically converted to the appropriate pin type
3. Special Node Handling: Some nodes have special property requirements

EXAMPLES:

1. String Literal Node:
   {
       "node_type": "MakeLiteralString",
       "node_properties": {
           "string_value": "Hello World"
       }
   }

2. Random Integer Node:
   {
       "node_type": "RandomIntegerInRange", 
       "node_properties": {
           "min": 1,
           "max": 100
       }
   }

3. Print String Node:
   {
       "node_type": "PrintString",
       "node_properties": {
           "InString": "Debug Message",
           "bPrintToScreen": true,
           "bPrintToLog": true,
           "Duration": 2.0
       }
   }

4. Variable Get/Set Nodes:
   {
       "node_type": "VariableGet",
       "node_properties": {
           "variable_name": "MyVariable"
       }
   }

COMMON PITFALLS:
- Pin names are case-sensitive and must match exactly
- Unknown pin names will be logged as warnings but won't cause errors
- Boolean values should be true/false (not "true"/"false" strings)
- Some nodes require specific properties (e.g., VariableGet needs variable_name)

ERROR HANDLING:
- Invalid JSON in node_properties will be logged as warnings
- Missing required properties for special nodes will cause creation failure
- Pin type mismatches are handled gracefully with conversion attempts

For detailed pin information, use the get_node_pins() function after node creation.
"""

# --- DYNAMIC BLUEPRINT NODE REGISTRY AND SEARCH ---
_blueprint_registry = None

def build_blueprint_node_registry():
    registry = []
    # Try to use the most compatible Unreal Python API for class introspection
    try:
        # Unreal 4.27+ and 5.x: use all_classes()
        all_classes = getattr(unreal, 'all_classes', None)
        if callable(all_classes):
            for uclass in unreal.all_classes():
                for func in uclass.get_functions():
                    if func.has_meta_data("BlueprintCallable"):
                        registry.append({
                            "class": uclass.get_name(),
                            "function_name": func.get_name(),
                            "function_class": uclass.get_name(),
                            "description": func.get_doc() if hasattr(func, "get_doc") else "",
                        })
            return registry
        else:
            log.log_warning("[DYNAMIC NODE REGISTRY] 'unreal.all_classes()' not available. Falling back to static registry.")
    except Exception as e:
        log.log_warning(f"[DYNAMIC NODE REGISTRY] Error during class introspection: {e}. Falling back to static registry.")
    # Fallback: static registry for core nodes
    return [
        {
            'class': 'KismetMathLibrary',
            'function_name': 'RandomIntegerInRange',
            'function_class': 'KismetMathLibrary',
            'description': 'Returns a random integer between Min and Max',
        },
        {
            'class': 'KismetSystemLibrary',
            'function_name': 'PrintString',
            'function_class': 'KismetSystemLibrary',
            'description': 'Prints a string to the screen',
        },
    ]

def get_blueprint_registry():
    global _blueprint_registry
    if _blueprint_registry is None:
        _blueprint_registry = build_blueprint_node_registry()
    return _blueprint_registry

def search_blueprint_nodes(query, top_n=3):
    registry = get_blueprint_registry()
    search_strings = [
        f"{entry['function_name']} {entry['function_class']} {entry['description']}"
        for entry in registry
    ]
    matches = get_close_matches(query.lower(), [s.lower() for s in search_strings], n=top_n, cutoff=0.3)
    results = []
    for match in matches:
        for entry in registry:
            if match in f"{entry['function_name']} {entry['function_class']} {entry['description']}":
                results.append(entry)
                break
    return results

# --- MCP COMMAND: SEARCH BLUEPRINT NODES ---
def handle_search_blueprint_nodes(command):
    """
    MCP command to search Blueprint nodes/functions by query string.
    Args:
        command: { 'query': <string>, 'top_n': <int> }
    Returns:
        { 'success': True, 'results': [ ... ] }
    """
    query = command.get('query', '')
    top_n = command.get('top_n', 3)
    results = search_blueprint_nodes(query, top_n=top_n)
    return { 'success': True, 'results': results }

# --- DYNAMIC NODE LOOKUP FOR FUNCTION GENERATION ---
def find_blueprint_node_dynamic(query):
    results = search_blueprint_nodes(query, top_n=1)
    if results and results[0].get('function_name'):
        entry = results[0]
        return {
            'node_type': 'K2Node_CallFunction',
            'function_name': entry['function_name'],
            'function_class': entry['function_class'],
        }
    log.log_warning(f"[AI Plugin] No matching Blueprint node found for: '{query}'. Node creation skipped.")
    return {}

def handle_create_blueprint(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to create a new Blueprint from a specified parent class
    
    Args:
        command: The command dictionary containing:
            - blueprint_name: Name for the new Blueprint
            - parent_class: Parent class name or path (e.g., "Actor", "/Script/Engine.Actor")
            - save_path: Path to save the Blueprint asset (e.g., "/Game/Blueprints")
            
    Returns:
        Response dictionary with success/failure status and the Blueprint path if successful
    """
    try:
        blueprint_name = command.get("blueprint_name", "NewBlueprint")
        parent_class = command.get("parent_class", "Actor")
        save_path = command.get("save_path", "/Game/Blueprints")

        log.log_command("create_blueprint", f"Name: {blueprint_name}, Parent: {parent_class}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        blueprint = gen_bp_utils.create_blueprint(blueprint_name, parent_class, save_path)

        if blueprint:
            blueprint_path = f"{save_path}/{blueprint_name}"
            log.log_result("create_blueprint", True, f"Path: {blueprint_path}")
            return {"success": True, "blueprint_path": blueprint_path}
        else:
            log.log_error(f"Failed to create Blueprint {blueprint_name}")
            return {"success": False, "error": f"Failed to create Blueprint {blueprint_name}"}

    except Exception as e:
        log.log_error(f"Error creating blueprint: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_add_component(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to add a component to a Blueprint
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - component_class: Component class to add (e.g., "StaticMeshComponent")
            - component_name: Name for the new component
            
    Returns:
        Response dictionary with success/failure status
    """
    try:
        blueprint_path = command.get("blueprint_path")
        component_class = command.get("component_class")
        component_name = command.get("component_name")

        if not blueprint_path or not component_class:
            log.log_error("Missing required parameters for add_component")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("add_component", f"Blueprint: {blueprint_path}, Component: {component_class}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        success = gen_bp_utils.add_component(blueprint_path, component_class, component_name or "")

        if success:
            log.log_result("add_component", True, f"Added {component_class} to {blueprint_path}")
            return {"success": True}
        else:
            log.log_error(f"Failed to add component {component_class} to {blueprint_path}")
            return {"success": False, "error": f"Failed to add component {component_class} to {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error adding component: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_add_variable(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to add a variable to a Blueprint
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - variable_name: Name for the new variable
            - variable_type: Type of the variable (e.g., "float", "vector", "boolean")
            - default_value: Default value for the variable (optional)
            - category: Category for organizing variables in the Blueprint editor (optional)
            
    Returns:
        Response dictionary with success/failure status
    """
    try:
        blueprint_path = command.get("blueprint_path")
        variable_name = command.get("variable_name")
        variable_type = command.get("variable_type")
        default_value = command.get("default_value", "")
        category = command.get("category", "Default")

        if not blueprint_path or not variable_name or not variable_type:
            log.log_error("Missing required parameters for add_variable")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("add_variable",
                        f"Blueprint: {blueprint_path}, Variable: {variable_name}, Type: {variable_type}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        success = gen_bp_utils.add_variable(blueprint_path, variable_name, variable_type, str(default_value), category)

        if success:
            log.log_result("add_variable", True, f"Added {variable_type} variable {variable_name} to {blueprint_path}")
            return {"success": True}
        else:
            log.log_error(f"Failed to add variable {variable_name} to {blueprint_path}")
            return {"success": False, "error": f"Failed to add variable {variable_name} to {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error adding variable: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_add_function(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to add a function to a Blueprint
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_name: Name for the new function
            - inputs: List of input parameters [{"name": "param1", "type": "float"}, ...] (optional)
            - outputs: List of output parameters (optional)
            
    Returns:
        Response dictionary with success/failure status and the function ID if successful
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_name = command.get("function_name")
        inputs = command.get("inputs", [])
        outputs = command.get("outputs", [])

        # Always serialize inputs/outputs to JSON strings
        if not isinstance(inputs, str):
            inputs_json = json.dumps(inputs)
        else:
            inputs_json = inputs
        if not isinstance(outputs, str):
            outputs_json = json.dumps(outputs)
        else:
            outputs_json = outputs

        if not blueprint_path or not function_name:
            log.log_error("Missing required parameters for add_function")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("add_function", f"Blueprint: {blueprint_path}, Function: {function_name}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        function_id = gen_bp_utils.add_function(blueprint_path, function_name, inputs_json, outputs_json)

        if function_id:
            log.log_result("add_function", True,
                           f"Added function {function_name} to {blueprint_path} with ID: {function_id}")
            return {"success": True, "function_id": function_id}
        else:
            log.log_error(f"Failed to add function {function_name} to {blueprint_path}")
            return {"success": False, "error": f"Failed to add function {function_name} to {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error adding function: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_add_node(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to add any type of node to a Blueprint graph
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function to add the node to
            - node_type: Type of node to add
            - node_position: Position of the node in the graph [X, Y]
            - node_properties: Dictionary of properties to set on the node (optional)
                * Maps property names to pin names for setting default values
                * Supports string, number, and boolean values
                * Special handling for VariableGet/Set nodes (use "variable_name")
                * See module docstring for detailed examples and usage patterns
            - target_class: Optional class to use for function calls (default: "Actor")
    Returns:
        Response dictionary with success/failure status, the node ID if successful, and pin metadata
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        node_type = command.get("node_type")
        node_position = command.get("node_position", [0, 0])
        node_properties = command.get("node_properties", {})

        if not blueprint_path or not function_id or not node_type:
            log.log_error("Missing required parameters for add_node")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("add_node", f"Blueprint: {blueprint_path}, Node: {node_type}")

        # Convert node properties to JSON for C++ function
        node_properties_json = json.dumps(node_properties)

        # Call the C++ implementation from UGenBlueprintNodeCreator
        node_creator = unreal.GenBlueprintNodeCreator
        node_id = node_creator.add_node(blueprint_path, function_id, node_type,
                                        node_position[0], node_position[1],
                                        node_properties_json)

        if node_id:
            # --- NEW: Fetch pin metadata for the created node ---
            pin_metadata = get_node_pins(blueprint_path, function_id, node_id)
            log.log_result("add_node", True, f"Added node {node_type} to {blueprint_path} with ID: {node_id}")
            return {"success": True, "node_id": node_id, "pin_metadata": pin_metadata}
        else:
            log.log_error(f"Failed to add node {node_type} to {blueprint_path}")
            return {"success": False, "error": f"Failed to add node {node_type} to {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error adding node: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

# Helper to fetch pin metadata for a node (stub, to be implemented)
def get_node_pins(blueprint_path, function_id, node_id):
    """
    Fetch input/output pin names/types for a node using the C++ bridge.
    Returns: { "inputs": [...], "outputs": [...] }
    """
    try:
        node_creator = unreal.GenBlueprintNodeCreator
        pins_json = node_creator.get_node_pins(blueprint_path, function_id, node_id)
        import json
        pin_metadata = json.loads(pins_json)
        # Optionally filter/validate structure here
        return pin_metadata
    except Exception as e:
        log.log_error(f"Error fetching pin metadata: {str(e)}")
        return {"inputs": [], "outputs": [], "error": str(e)}


def handle_connect_nodes(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to connect two Blueprint nodes using improved Python API
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function containing the nodes
            - source_node_id: GUID of the source node
            - source_pin: Name of the output pin on the source node
            - target_node_id: GUID of the target node
            - target_pin: Name of the input pin on the target node
            
    Returns:
        Response dictionary with success/failure status and connection details
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        source_node_id = command.get("source_node_id")
        source_pin = command.get("source_pin", "exec")  # Default to exec pin
        target_node_id = command.get("target_node_id")
        target_pin = command.get("target_pin", "exec")  # Default to exec pin

        if not all([blueprint_path, function_id, source_node_id, target_node_id]):
            log.log_error("Missing required parameters for connect_nodes")
            return {"success": False, "error": "Missing required parameters: blueprint_path, function_id, source_node_id, target_node_id"}

        log.log_command("connect_nodes",
                        f"Blueprint: {blueprint_path}, {source_node_id}.{source_pin} -> {target_node_id}.{target_pin}")

        # Try using the improved connection function first
        try:
            from improved_blueprint_connections import connect_blueprint_nodes_improved
            
            result = connect_blueprint_nodes_improved(
                blueprint_path=blueprint_path,
                function_name=function_id,  # Use function_id as function_name for compatibility
                source_node_guid=source_node_id,
                source_pin=source_pin,
                target_node_guid=target_node_id,
                target_pin=target_pin
            )
            
            if result.get("success"):
                log.log_result("connect_nodes", True, f"Connected nodes in {blueprint_path}")
                return result
            else:
                log.log_warning(f"Improved connection failed: {result.get('error')}")
                
        except ImportError as e:
            log.log_warning(f"Could not import improved_blueprint_connections: {e}")
        except Exception as e:
            log.log_warning(f"Error with improved Blueprint connection: {e}")
            
        # Fallback to original method if improved version fails
        log.log_info("Falling back to original Blueprint connection method")
        
        gen_bp_utils = unreal.GenBlueprintUtils
        result_json = gen_bp_utils.connect_nodes(blueprint_path, function_id,
                                                 source_node_id, source_pin,
                                                 target_node_id, target_pin)
        result = json.loads(result_json)

        if result.get("success"):
            log.log_result("connect_nodes", True, f"Connected nodes in {blueprint_path} (fallback method)")
            return {"success": True}
        else:
            log.log_error(f"Failed to connect nodes: {result.get('error')}")
            return result  # Pass through the detailed response with available pins

    except Exception as e:
        log.log_error(f"Error connecting nodes: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_compile_blueprint(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to compile a Blueprint
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            
    Returns:
        Response dictionary with success/failure status
    """
    try:
        blueprint_path = command.get("blueprint_path")

        if not blueprint_path:
            log.log_error("Missing required parameters for compile_blueprint")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("compile_blueprint", f"Blueprint: {blueprint_path}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        success = gen_bp_utils.compile_blueprint(blueprint_path)

        if success:
            log.log_result("compile_blueprint", True, f"Compiled blueprint: {blueprint_path}")
            return {"success": True}
        else:
            log.log_error(f"Failed to compile blueprint: {blueprint_path}")
            return {"success": False, "error": f"Failed to compile blueprint: {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error compiling blueprint: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_spawn_blueprint(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to spawn a Blueprint actor in the level
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - location: [X, Y, Z] coordinates (optional)
            - rotation: [Pitch, Yaw, Roll] in degrees (optional)
            - scale: [X, Y, Z] scale factors (optional)
            - actor_label: Optional custom name for the actor
            
    Returns:
        Response dictionary with success/failure status and the actor name if successful
    """
    try:
        blueprint_path = command.get("blueprint_path")
        location = command.get("location", (0, 0, 0))
        rotation = command.get("rotation", (0, 0, 0))
        scale = command.get("scale", (1, 1, 1))
        actor_label = command.get("actor_label", "")

        if not blueprint_path:
            log.log_error("Missing required parameters for spawn_blueprint")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("spawn_blueprint", f"Blueprint: {blueprint_path}, Label: {actor_label}")

        # Convert to Unreal types
        loc = uc.to_unreal_vector(location)
        rot = uc.to_unreal_rotator(rotation)
        scale_vec = uc.to_unreal_vector(scale)

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        actor = gen_bp_utils.spawn_blueprint(blueprint_path, loc, rot, scale_vec, actor_label)

        if actor:
            actor_name = actor.get_actor_label()
            log.log_result("spawn_blueprint", True, f"Spawned blueprint: {blueprint_path} as {actor_name}")
            return {"success": True, "actor_name": actor_name}
        else:
            log.log_error(f"Failed to spawn blueprint: {blueprint_path}")
            return {"success": False, "error": f"Failed to spawn blueprint: {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error spawning blueprint: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_add_nodes_bulk(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to add multiple nodes to a Blueprint graph in a single operation
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function to add the nodes to
            - nodes: Array of node definitions, each containing:
                * id: Optional ID for referencing the node (string)
                * node_type: Type of node to add (string)
                * node_position: Position of the node in the graph [X, Y]
                * node_properties: Properties to set on the node (optional)
            
    Returns:
        Response dictionary with success/failure status and node IDs mapped to reference IDs
    """

    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        nodes = command.get("nodes", [])

        if not blueprint_path or not function_id or not nodes:
            log.log_error("Missing required parameters for add_nodes_bulk")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("add_nodes_bulk", f"Blueprint: {blueprint_path}, Adding {len(nodes)} nodes")

        # Prepare nodes in the format expected by the C++ function
        nodes_json = json.dumps(nodes)

        # Call the C++ implementation from UGenBlueprintNodeCreator
        node_creator = unreal.GenBlueprintNodeCreator
        results_json = node_creator.add_nodes_bulk(blueprint_path, function_id, nodes_json)
        
        if results_json:
            results = json.loads(results_json)
            node_mapping = {}

            # Create a mapping from reference IDs to actual node GUIDs
            for node_result in results:
                if "ref_id" in node_result:
                    node_mapping[node_result["ref_id"]] = node_result["node_guid"]
                else:
                    # For nodes without a reference ID, just include the GUID
                    node_mapping[f"node_{len(node_mapping)}"] = node_result["node_guid"]

            log.log_result("add_nodes_bulk", True, f"Added {len(results)} nodes to {blueprint_path}")
            return {"success": True, "nodes": node_mapping}
        else:
            log.log_error(f"Failed to add nodes to {blueprint_path}")
            return {"success": False, "error": f"Failed to add nodes to {blueprint_path}"}

    except Exception as e:
        log.log_error(f"Error adding nodes: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_connect_nodes_bulk(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to connect multiple pairs of nodes in a Blueprint graph using improved Python API
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function containing the nodes
            - connections: Array of connection definitions, each containing:
                * source_node_id: ID of the source node
                * source_pin: Name of the source pin (optional, defaults to "exec")
                * target_node_id: ID of the target node
                * target_pin: Name of the target pin (optional, defaults to "exec")
            
    Returns:
        Response dictionary with detailed connection results
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        connections = command.get("connections", [])

        if not blueprint_path or not function_id or not connections:
            log.log_error("Missing required parameters for connect_nodes_bulk")
            return {"success": False, "error": "Missing required parameters: blueprint_path, function_id, connections"}

        log.log_command("connect_nodes_bulk", f"Blueprint: {blueprint_path}, Making {len(connections)} connections")

        # Try using the improved bulk connection function first
        try:
            from improved_blueprint_connections import connect_blueprint_nodes_bulk_improved
            
            # Prepare connections with defaults for pins if not specified
            prepared_connections = []
            for conn in connections:
                prepared_conn = {
                    "source_node_guid": conn.get("source_node_id"),
                    "source_pin": conn.get("source_pin", "exec"),
                    "target_node_guid": conn.get("target_node_id"), 
                    "target_pin": conn.get("target_pin", "exec")
                }
                prepared_connections.append(prepared_conn)
            
            result = connect_blueprint_nodes_bulk_improved(
                blueprint_path=blueprint_path,
                function_name=function_id,  # Use function_id as function_name for compatibility
                connections=prepared_connections
            )
            
            if result.get("success") or result.get("successful_connections", 0) > 0:
                log.log_result("connect_nodes_bulk", result.get("success", False),
                              f"Connected {result.get('successful_connections', 0)}/{result.get('total_connections', 0)} node pairs")
                return result
            else:
                log.log_warning(f"Improved bulk connection failed: {result.get('error')}")
                
        except ImportError as e:
            log.log_warning(f"Could not import improved_blueprint_connections: {e}")
        except Exception as e:
            log.log_warning(f"Error with improved bulk Blueprint connection: {e}")
            
        # Fallback to original method if improved version fails
        log.log_info("Falling back to original bulk connection method")

        # Convert connections list to JSON for C++ function
        connections_json = json.dumps(connections)

        # Call the C++ implementation - now returns a JSON string instead of boolean
        gen_bp_utils = unreal.GenBlueprintUtils
        result_json = gen_bp_utils.connect_nodes_bulk(blueprint_path, function_id, connections_json)

        # Parse the JSON result
        try:
            result_data = json.loads(result_json)
            log.log_result("connect_nodes_bulk", result_data.get("success", False),
                           f"Connected {result_data.get('successful_connections', 0)}/{result_data.get('total_connections', 0)} node pairs in {blueprint_path}")

            # Return the full result data for detailed error reporting
            return result_data
        except json.JSONDecodeError:
            log.log_error(f"Failed to parse JSON result from connect_nodes_bulk: {result_json}")
            return {"success": False, "error": "Failed to parse connection results"}

    except Exception as e:
        log.log_error(f"Error connecting nodes: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_delete_node(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to delete a node from a Blueprint graph
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function containing the node
            - node_id: ID of the node to delete
                
    Returns:
        Response dictionary with success/failure status
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        node_id = command.get("node_id")

        if not blueprint_path or not function_id or not node_id:
            log.log_error("Missing required parameters for delete_node")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("delete_node", f"Blueprint: {blueprint_path}, Node ID: {node_id}")

        # Call the C++ implementation from UGenBlueprintNodeCreator
        node_creator = unreal.GenBlueprintNodeCreator
        success = node_creator.delete_node(blueprint_path, function_id, node_id)

        if success:
            log.log_result("delete_node", True, f"Deleted node {node_id} from {blueprint_path}")
            return {"success": True}
        else:
            log.log_error(f"Failed to delete node {node_id} from {blueprint_path}")
            return {"success": False, "error": f"Failed to delete node {node_id}"}

    except Exception as e:
        log.log_error(f"Error deleting node: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_get_all_nodes(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to get all nodes in a Blueprint graph
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function to get nodes from
                
    Returns:
        Response dictionary with success/failure status and a list of nodes with their details
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")

        if not blueprint_path or not function_id:
            log.log_error("Missing required parameters for get_all_nodes")
            return {"success": False, "error": "Missing required parameters"}

        log.log_command("get_all_nodes", f"Blueprint: {blueprint_path}, Function ID: {function_id}")

        # Call the C++ implementation from UGenBlueprintNodeCreator
        node_creator = unreal.GenBlueprintNodeCreator
        nodes_json = node_creator.get_all_nodes_in_graph(blueprint_path, function_id)

        if nodes_json:
            # Parse the JSON response
            try:
                nodes = json.loads(nodes_json)
                log.log_result("get_all_nodes", True, f"Retrieved {len(nodes)} nodes from {blueprint_path}")
                return {"success": True, "nodes": nodes}
            except json.JSONDecodeError as e:
                log.log_error(f"Error parsing nodes JSON: {str(e)}")
                return {"success": False, "error": f"Error parsing nodes JSON: {str(e)}"}
        else:
            log.log_error(f"Failed to get nodes from {blueprint_path}")
            return {"success": False, "error": "Failed to get nodes"}

    except Exception as e:
        log.log_error(f"Error getting nodes: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_get_node_suggestions(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to get suggestions for a node type in Unreal Blueprints
    
    Args:
        command: The command dictionary containing:
            - node_type: The partial or full node type to get suggestions for (e.g., "Add", "FloatToDouble")
                
    Returns:
        Response dictionary with success/failure status and a list of suggested node types
    """
    try:
        node_type = command.get("node_type")

        if not node_type:
            log.log_error("Missing required parameter 'node_type' for get_node_suggestions")
            return {"success": False, "error": "Missing required parameter 'node_type'"}

        log.log_command("get_node_suggestions", f"Node Type: {node_type}")

        # Call the C++ implementation from UGenBlueprintNodeCreator
        node_creator = unreal.GenBlueprintNodeCreator
        suggestions_result = node_creator.get_node_suggestions(node_type)

        if suggestions_result:
            if suggestions_result.startswith("SUGGESTIONS:"):
                suggestions = suggestions_result[len("SUGGESTIONS:"):].split(", ")
                log.log_result("get_node_suggestions", True, f"Retrieved {len(suggestions)} suggestions for {node_type}")
                return {"success": True, "suggestions": suggestions}
            else:
                log.log_error(f"Unexpected response format from get_node_suggestions: {suggestions_result}")
                return {"success": False, "error": "Unexpected response format from Unreal"}
        else:
            log.log_result("get_node_suggestions", False, f"No suggestions found for {node_type}")
            return {"success": True, "suggestions": []}  # Empty list for no matches

    except Exception as e:
        log.log_error(f"Error getting node suggestions: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_get_node_guid(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to retrieve the GUID of a pre-existing node in a Blueprint graph.
    
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - graph_type: "EventGraph" or "FunctionGraph"
            - node_name: Name of the node (e.g., "BeginPlay") for EventGraph
            - function_id: ID of the function for FunctionGraph to get FunctionEntry
            
    Returns:
        Response dictionary with the node's GUID or an error
    """
    try:
        blueprint_path = command.get("blueprint_path")
        graph_type = command.get("graph_type", "EventGraph")
        node_name = command.get("node_name", "")
        function_id = command.get("function_id", "")

        if not blueprint_path:
            log.log_error("Missing blueprint_path for get_node_guid")
            return {"success": False, "error": "Missing blueprint_path"}

        if graph_type not in ["EventGraph", "FunctionGraph"]:
            log.log_error(f"Invalid graph_type: {graph_type}")
            return {"success": False, "error": f"Invalid graph_type: {graph_type}"}

        log.log_command("get_node_guid", f"Blueprint: {blueprint_path}, Graph: {graph_type}, Node: {node_name or function_id}")

        # Call the C++ implementation
        gen_bp_utils = unreal.GenBlueprintUtils
        node_guid = gen_bp_utils.get_node_guid(blueprint_path, graph_type, node_name, function_id)

        if node_guid:
            log.log_result("get_node_guid", True, f"Found node GUID: {node_guid}")
            return {"success": True, "node_guid": node_guid}
        else:
            log.log_error(f"Failed to find node: {node_name or 'FunctionEntry'}")
            return {"success": False, "error": f"Node not found: {node_name or 'FunctionEntry'}"}

    except Exception as e:
        log.log_error(f"Error getting node GUID: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}

def handle_generate_smart_blueprint_function(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to generate a smart Blueprint function with AI-driven node creation - LOCAL IMPLEMENTATION
    Uses direct Unreal Engine Python API calls, similar to create_material pattern
    
    Args:
        command: The command dictionary containing:
            - function_name: Name for the new function
            - description: Description of what the function should do
            - inputs: List of input parameters [{"name": "param1", "type": "int32"}, ...]
            - outputs: List of output parameters [{"name": "result", "type": "string"}, ...]
            - complexity: Function complexity ("simple", "medium", "complex")
            - use_current_context: Whether to use current Blueprint context (optional)
            - blueprint_path: Specific Blueprint path (optional)
            
    Returns:
        Response dictionary with success/failure status and generation details
    """
    try:
        function_name = command.get("function_name", "AIGeneratedFunction")
        description = command.get("description", "")
        inputs = command.get("inputs", [])
        outputs = command.get("outputs", [])
        complexity = command.get("complexity", "medium")
        
        use_current_context = command.get("use_current_context", True)
        blueprint_path = command.get("blueprint_path", "")

        log.log_command("generate_smart_blueprint_function", f"Function: {function_name}, Description: {description}")

        if not function_name or not function_name.strip():
            return {"success": False, "error": "Function name cannot be empty"}
        if not description or not description.strip():
            return {"success": False, "error": "Function description cannot be empty"}

        # STEP 1: Get current Unreal Engine context using C++ sync system
        log.log_info("🔄 Step 1: Getting current Unreal Engine context...")
        context_result = handle_get_unreal_context({})
        context_asset_path = None
        context_asset_type = None
        context_asset_name = None
        
        # Log the full context for debugging
        log.log_info(f"[DEBUG] Full context_result: {json.dumps(context_result, indent=2, default=str)}")

        if context_result.get("success", False):
            log.log_info("✅ Successfully retrieved UE context")
            context_data = context_result.get("context", {})
            blueprint_context = context_data.get("blueprint_context", {})
            editor_context = context_data.get("editor_context", {})

            # Prefer the asset that was open in the window where Sync with AI was clicked
            # Look for 'current_blueprint' or the first open asset
            current_blueprint = blueprint_context.get("current_blueprint", {})
            if current_blueprint and current_blueprint.get("path"):
                context_asset_path = current_blueprint["path"]
                context_asset_type = current_blueprint.get("type", "Blueprint")
                context_asset_name = current_blueprint.get("name", "")
                log.log_info(f"🎯 Using asset from Sync with AI window: {context_asset_name} ({context_asset_type}) at {context_asset_path}")
            else:
                # Try to get from open assets (e.g., Widget, other asset types)
                open_assets = editor_context.get("open_assets", [])
                if open_assets:
                    context_asset = open_assets[0]
                    context_asset_path = context_asset.get("path")
                    context_asset_type = context_asset.get("type", "Unknown")
                    context_asset_name = context_asset.get("name", "")
                    log.log_info(f"🎯 Using first open asset: {context_asset_name} ({context_asset_type}) at {context_asset_path}")

            # --- Robust fallback: search for any valid asset path in the context dict ---
            if not context_asset_path:
                def find_asset_path(d):
                    if isinstance(d, dict):
                        for k, v in d.items():
                            if isinstance(v, str) and "/" in v and (".umap" in v or ".Lvl_" in v or ".Blueprint" in v or ":PersistentLevel" in v):
                                return v
                            elif isinstance(v, dict):
                                found = find_asset_path(v)
                                if found:
                                    return found
                            elif isinstance(v, list):
                                for item in v:
                                    found = find_asset_path(item)
                                    if found:
                                        return found
                    return None
                context_asset_path = find_asset_path(context_result)
                if context_asset_path:
                    log.log_info(f"[DEBUG] Fallback: Found asset path in context: {context_asset_path}")

        # Always use the asset from context if available
        if context_asset_path:
            blueprint_path = context_asset_path
            log.log_info(f"✅ Targeting asset from Sync with AI: {context_asset_name} ({context_asset_type}) at {blueprint_path}")
        else:
            log.log_warning("⚠️ No asset found from Sync with AI context. Falling back to default.")
            blueprint_path = "/Game/Blueprints/BP_Default"
            log.log_info(f"Using fallback blueprint path: {blueprint_path}")

        log.log_info(f"Target Blueprint path: {blueprint_path}")

        # STEP 2: Create the function with proper error handling
        try:
            add_function_result = handle_add_function({
                "blueprint_path": blueprint_path,
                "function_name": function_name,
                "inputs": inputs,
                "outputs": outputs
            })

            if not add_function_result.get("success", False):
                log.log_error(f"Failed to create function: {add_function_result}")
                return {
                    "success": False,
                    "error": f"Failed to create Blueprint function: {add_function_result.get('error', 'Unknown error')}",
                    "blueprint_path": blueprint_path
                }

            function_id = add_function_result.get("function_id")
            log.log_info(f"✅ Function created with ID: {function_id}")
            
        except Exception as e:
            log.log_error(f"Exception during function creation: {str(e)}")
            return {
                "success": False,
                "error": f"Exception creating function: {str(e)}",
                "blueprint_path": blueprint_path
            }

        # STEP 3: Generate nodes based on description and complexity with validation
        try:
            nodes_to_add = _generate_nodes_for_function(function_name, description, inputs, outputs, complexity)
            log.log_info(f"Generated {len(nodes_to_add)} nodes to add: {[node.get('node_type', 'Unknown') for node in nodes_to_add]}")

            if not nodes_to_add:
                log.log_info("No nodes to add - creating simple function")
                return {
                    "success": True,
                    "message": f"Function '{function_name}' created successfully (empty)",
                    "function_name": function_name,
                    "function_id": function_id,
                    "nodes_created": 0,
                    "connections_made": 0,
                    "blueprint_path": blueprint_path
                }
        except Exception as e:
            log.log_error(f"Exception during node generation: {str(e)}")
            return {
                "success": True,
                "message": f"Function '{function_name}' created (node generation failed)",
                "function_name": function_name,
                "function_id": function_id,
                "nodes_created": 0,
                "connections_made": 0,
                "blueprint_path": blueprint_path,
                "warning": f"Node generation failed: {str(e)}"
            }

        # STEP 4: Add nodes to the function with error handling
        nodes_created = 0
        node_mapping = {}
        try:
            add_nodes_result = handle_add_nodes_bulk({
                "blueprint_path": blueprint_path,
                "function_id": function_id,
                "nodes": nodes_to_add
            })
            if add_nodes_result.get("success", False):
                node_mapping = add_nodes_result.get("nodes", {})
                nodes_created = len(node_mapping)
                log.log_info(f"✅ Nodes added: {list(node_mapping.keys())}")
            else:
                log.log_warning(f"⚠️ Node creation failed: {add_nodes_result}")
        except Exception as e:
            log.log_error(f"Exception during node creation: {str(e)}")

        # STEP 5: Connect the nodes with error handling
        connections_made = 0
        if nodes_created > 1 and node_mapping:
            try:
                connections = _generate_connections_for_nodes(nodes_to_add, node_mapping, inputs, outputs)
                if connections:
                    connect_result = handle_connect_nodes_bulk({
                        "blueprint_path": blueprint_path,
                        "function_id": function_id,
                        "connections": connections
                    })
                    if connect_result.get("success", False):
                        connections_made = connect_result.get("successful_connections", len(connections))
                        log.log_info(f"✅ Nodes connected successfully: {connections_made}")
                    else:
                        log.log_warning(f"⚠️ Node connections failed: {connect_result}")
            except Exception as e:
                log.log_error(f"Exception during node connections: {str(e)}")

        # STEP 6: Compile the Blueprint with error handling
        compiled = False
        try:
            compile_result = handle_compile_blueprint({"blueprint_path": blueprint_path})
            compiled = compile_result.get("success", False)
            if compiled:
                log.log_info(f"✅ Blueprint compiled successfully")
            else:
                log.log_warning(f"⚠️ Blueprint compilation failed: {compile_result}")
        except Exception as e:
            log.log_error(f"Exception during Blueprint compilation: {str(e)}")

        log.log_result("generate_smart_blueprint_function", True, 
                      f"Function: {function_name}, Nodes: {nodes_created}, Connections: {connections_made}")
        return {
            "success": True,
            "message": f"Smart Blueprint function '{function_name}' generated successfully!",
            "function_name": function_name,
            "function_id": function_id,
            "description": description,
            "complexity": complexity,
            "nodes_created": nodes_created,
            "connections_made": connections_made,
            "compiled": compiled,
            "blueprint_path": blueprint_path,
            "generation_summary": f"Created {nodes_created} nodes with {connections_made} connections",
            "context_used": context_result.get("success", False)
        }
    except Exception as e:
        log.log_error(f"Error generating smart Blueprint function: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_generate_blueprint_function(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to generate a basic Blueprint function - LOCAL IMPLEMENTATION
    Simplified version that creates basic function structure
    
    Args:
        command: The command dictionary containing:
            - function_name: Name for the new function
            - description: Description of what the function should do
            - inputs: List of input parameters (optional)
            - outputs: List of output parameters (optional)
            - use_current_context: Whether to use current Blueprint context (optional)
            - blueprint_path: Specific Blueprint path (optional)
            
    Returns:
        Response dictionary with success/failure status
    """
    try:
        # Forward to smart generation with medium complexity
        enhanced_command = command.copy()
        enhanced_command["complexity"] = "medium"
        
        log.log_command("generate_blueprint_function", f"Function: {command.get('function_name', 'Unknown')}")
        
        result = handle_generate_smart_blueprint_function(enhanced_command)
        
        if result.get("success"):
            log.log_result("generate_blueprint_function", True, f"Generated via smart function")
        else:
            log.log_error(f"Basic function generation failed: {result.get('error')}")
            
        return result

    except Exception as e:
        log.log_error(f"Error generating Blueprint function: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def handle_generate_context_aware_blueprint_function(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to generate a context-aware Blueprint function - LOCAL IMPLEMENTATION
    Enhanced version that uses current Blueprint context for smarter generation
    
    Args:
        command: The command dictionary containing:
            - function_name: Name for the new function (optional, will be generated)
            - description: Description of what the function should do
            - complexity: Function complexity (optional)
            
    Returns:
        Response dictionary with success/failure status and context information
    """
    try:
        description = command.get("description", "")
        function_name = command.get("function_name", "")
        complexity = command.get("complexity", "medium")

        log.log_command("generate_context_aware_blueprint_function", f"Description: {description}")

        # Generate function name from description if not provided
        if not function_name:
            function_name = _generate_function_name_from_description(description)

        # Get current Blueprint context
        current_blueprint_path = ""
        try:
            level_bp = unreal.EditorLevelLibrary.get_level_script_blueprint()
            if level_bp:
                current_blueprint_path = level_bp.get_path_name()
                blueprint_name = level_bp.get_name()
            else:
                current_blueprint_path = "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson"
                blueprint_name = "Lvl_ThirdPerson"
        except:
            current_blueprint_path = "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson"
            blueprint_name = "Lvl_ThirdPerson"

        log.log_info(f"Context-aware generation for Blueprint: {blueprint_name}")

        # Enhance inputs/outputs based on description context
        enhanced_inputs, enhanced_outputs = _analyze_description_for_parameters(description)

        # Create the enhanced command
        enhanced_command = {
            "function_name": function_name,
            "description": description,
            "inputs": enhanced_inputs,
            "outputs": enhanced_outputs,
            "complexity": complexity,
            "blueprint_path": current_blueprint_path,
            "use_current_context": True
        }

        # Generate the function using smart generation
        result = handle_generate_smart_blueprint_function(enhanced_command)

        if result.get("success"):
            # Add context information to the result
            result["context_aware"] = True
            result["blueprint_context"] = {
                "blueprint_name": blueprint_name,
                "blueprint_path": current_blueprint_path,
                "auto_generated_name": not bool(command.get("function_name")),
                "enhanced_parameters": {
                    "inputs": enhanced_inputs,
                    "outputs": enhanced_outputs
                }
            }
            log.log_result("generate_context_aware_blueprint_function", True, 
                          f"Context-aware function: {function_name}")
        else:
            log.log_error(f"Context-aware function generation failed: {result.get('error')}")

        return result

    except Exception as e:
        log.log_error(f"Error generating context-aware Blueprint function: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def _get_safe_node_types() -> List[str]:
    """
    Return a list of known safe node types that work with our current implementation
    Updated to use exact names that match our C++ mappings
    """
    # Start with confirmed working node types (using exact C++ mapping names)
    safe_types = [
        "PrintString",      # Maps to KismetSystemLibrary.PrintString
        "Print String",     # Also maps to PrintString via C++ mapping
        "Branch",           # Maps to UK2Node_IfThenElse
        "Add",              # Maps to KismetMathLibrary.Add_FloatFloat
        "Multiply",         # Maps to KismetMathLibrary.Multiply_FloatFloat
        "RandomIntegerInRange", # Maps to KismetMathLibrary.RandomIntegerInRange
        "Random Integer in Range", # Also maps via C++ mapping
        "Append",           # Maps to KismetStringLibrary.Concat_StrStr
        "Delay",            # Maps to KismetSystemLibrary.Delay
    ]
    
    return safe_types


def _validate_node_type(node_type: str) -> bool:
    """
    Validate if a node type is safe to use
    """
    safe_types = _get_safe_node_types()
    return node_type in safe_types


def _generate_nodes_for_function(function_name: str, description: str, inputs: List[Dict], outputs: List[Dict], complexity: str) -> List[Dict]:
    """
    Generalized node generation for Blueprint functions.
    Uses dynamic node lookup for backend compatibility.
    """
    nodes = []
    desc_lower = description.lower()
    log.log_info(f"[GEN] Generating nodes for '{function_name}' | Desc: {description} | Inputs: {inputs} | Outputs: {outputs}")

    # 1. Entry node is always created by Unreal, do not add here

    # 2. Core logic node(s)
    if "random" in desc_lower and ("int" in desc_lower or "number" in desc_lower):
        node_info = find_blueprint_node_dynamic('random integer')
        log.log_info(f"[GEN] Adding {node_info.get('function_name')} node via {node_info.get('node_type')}")
        nodes.append({
            "node_type": node_info.get('node_type'),
            "function_name": node_info.get('function_name'),
            "function_class": node_info.get('function_class'),
            "node_id": f"random_node_{len(nodes)}",
            "node_position": [300, 0],
            "inputs": [
                {"name": "Min", "type": "int", "value": "MinValue"},
                {"name": "Max", "type": "int", "value": "MaxValue"}
            ],
            "outputs": [
                {"name": "ReturnValue", "type": "int"}
            ],
            "exec_input": "execute",
            "exec_output": "then"
        })
        random_node_id = f"random_node_{len(nodes)-1}"
    
    # 3. Print/logging node if description mentions it
    if "print" in desc_lower or "log" in desc_lower or "screen" in desc_lower:
        node_info = find_blueprint_node_dynamic('print string')
        log.log_info(f"[GEN] Adding {node_info.get('function_name')} node via {node_info.get('node_type')}")
        nodes.append({
            "node_type": node_info.get('node_type'),
            "function_name": node_info.get('function_name'),
            "function_class": node_info.get('function_class'),
            "node_id": f"print_node_{len(nodes)}",
            "node_position": [600, 0],
            "inputs": [
                {"name": "InString", "type": "string", "value": "RandomValue"},
                {"name": "bPrintToScreen", "type": "bool", "value": "true"},
                {"name": "bPrintToLog", "type": "bool", "value": "true"},
                {"name": "TextColor", "type": "LinearColor", "value": "(R=0.000000,G=0.660000,B=1.000000,A=1.000000)"},
                {"name": "Duration", "type": "float", "value": "2.0"}
            ],
            "outputs": [],
            "exec_input": "execute",
            "exec_output": "then"
        })
        print_node_id = f"print_node_{len(nodes)-1}"
    
    # 4. Assignment node for outputs (static for now, can be made dynamic)
    for output in outputs:
        log.log_info(f"[GEN] Adding K2Node_VariableSet for output {output['name']}")
        nodes.append({
            "node_type": "K2Node_VariableSet",
            "node_id": f"set_node_{len(nodes)}",
            "node_position": [900, 0],
            "variable_name": output['name'],
            "inputs": [
                {"name": "Variable", "type": output['type'], "value": "ReturnValue"}
            ],
            "outputs": [],
            "exec_input": "execute",
            "exec_output": "then"
        })
        set_node_id = f"set_node_{len(nodes)-1}"
    
    log.log_info(f"[GEN] Total nodes generated: {len(nodes)}")
    for node in nodes:
        log.log_info(f"[DEBUG] node_position for node {node.get('node_id')}: {node.get('node_position')} (type: {type(node.get('node_position'))})")
    return nodes


def _generate_connections_for_nodes(nodes: List[Dict], inputs: List[Dict], outputs: List[Dict]) -> List[Dict]:
    """
    Generate connections between nodes for a complete Blueprint function graph.
    Uses the new node structure with internal Unreal node names and proper pin specifications.
    """
    connections = []
    log.log_info(f"[CONN] Generating connections for {len(nodes)} nodes")
    
    if not nodes:
        log.log_info("[CONN] No nodes to connect")
        return connections
    
    # Find node IDs for different types
    random_node_id = None
    print_node_id = None
    set_node_id = None
    
    for node in nodes:
        if node.get("node_type") == "RandomIntegerInRange":
            random_node_id = node.get("node_id")
        elif node.get("node_type") == "PrintString":
            print_node_id = node.get("node_id")
        elif node.get("node_type") == "K2Node_VariableSet":
            set_node_id = node.get("node_id")
    
    log.log_info(f"[CONN] Found nodes - Random: {random_node_id}, Print: {print_node_id}, Set: {set_node_id}")
    
    # 1. Connect execution flow: Entry -> Logic -> Print -> Set -> Return
    current_exec_source = "entry"  # Function entry point
    
    if random_node_id:
        # Entry -> Random node
        connections.append({
            "connection_type": "exec",
            "from_node": current_exec_source,
            "from_pin": "then",
            "to_node": random_node_id,
            "to_pin": "execute"
        })
        log.log_info(f"[CONN] Connected execution: {current_exec_source}.then -> {random_node_id}.execute")
        current_exec_source = random_node_id
    
    if print_node_id:
        # Previous -> Print node
        connections.append({
            "connection_type": "exec",
            "from_node": current_exec_source,
            "from_pin": "then",
            "to_node": print_node_id,
            "to_pin": "execute"
        })
        log.log_info(f"[CONN] Connected execution: {current_exec_source}.then -> {print_node_id}.execute")
        current_exec_source = print_node_id
    
    if set_node_id:
        # Previous -> Set node
        connections.append({
            "connection_type": "exec",
            "from_node": current_exec_source,
            "from_pin": "then",
            "to_node": set_node_id,
            "to_pin": "execute"
        })
        log.log_info(f"[CONN] Connected execution: {current_exec_source}.then -> {set_node_id}.execute")
        current_exec_source = set_node_id
    
    # 2. Connect data pins
    if random_node_id and set_node_id:
        # Random output -> Set input
        connections.append({
            "connection_type": "data",
            "from_node": random_node_id,
            "from_pin": "ReturnValue",
            "to_node": set_node_id,
            "to_pin": "Variable"
        })
        log.log_info(f"[CONN] Connected data: {random_node_id}.ReturnValue -> {set_node_id}.Variable")
    
    if set_node_id and print_node_id:
        # Set output -> Print input (convert to string)
        connections.append({
            "connection_type": "data",
            "from_node": set_node_id,
            "from_pin": "Variable",
            "to_node": print_node_id,
            "to_pin": "InString"
        })
        log.log_info(f"[CONN] Connected data: {set_node_id}.Variable -> {print_node_id}.InString")
    
    # 3. Connect input parameters to nodes
    for input_param in inputs:
        if input_param["name"] == "MinValue" and random_node_id:
            connections.append({
                "connection_type": "data",
                "from_node": "input",
                "from_pin": "MinValue",
                "to_node": random_node_id,
                "to_pin": "Min"
            })
            log.log_info(f"[CONN] Connected input: input.MinValue -> {random_node_id}.Min")
        
        elif input_param["name"] == "MaxValue" and random_node_id:
            connections.append({
                "connection_type": "data",
                "from_node": "input",
                "from_pin": "MaxValue",
                "to_node": random_node_id,
                "to_pin": "Max"
            })
            log.log_info(f"[CONN] Connected input: input.MaxValue -> {random_node_id}.Max")
    
    log.log_info(f"[CONN] Total connections generated: {len(connections)}")
    return connections


def _generate_function_name_from_description(description: str) -> str:
    """
    Generate a function name from the description - LOCAL UTILITY
    """
    import re
    
    # Extract key words and convert to PascalCase
    words = re.findall(r'\b\w+\b', description.lower())
    
    # Remove common words
    stop_words = {'a', 'an', 'the', 'and', 'or', 'but', 'for', 'to', 'of', 'in', 'on', 'at', 'by', 'with', 'that', 'this', 'is', 'are', 'was', 'will', 'be'}
    meaningful_words = [word for word in words if word not in stop_words and len(word) > 2]
    
    # Take first 3-4 meaningful words and capitalize
    function_words = meaningful_words[:4]
    function_name = ''.join(word.capitalize() for word in function_words)
    
    # Ensure it starts with uppercase and has reasonable length
    if len(function_name) < 3:
        function_name = "GeneratedFunction"
    elif len(function_name) > 50:
        function_name = function_name[:50]
    
    return function_name


def _analyze_description_for_parameters(description: str) -> Tuple[List[Dict], List[Dict]]:
    """
    Analyze function description to determine appropriate input/output parameters - LOCAL AI LOGIC
    """
    desc_lower = description.lower()
    inputs = []
    outputs = []
    
    # Analyze for common patterns
    if "password" in desc_lower:
        inputs = [
            {"name": "PasswordLength", "type": "int32"},
            {"name": "IncludeUppercase", "type": "bool"},
            {"name": "IncludeLowercase", "type": "bool"},
            {"name": "IncludeNumbers", "type": "bool"},
            {"name": "IncludeSpecialChars", "type": "bool"}
        ]
        outputs = [
            {"name": "GeneratedPassword", "type": "string"},
            {"name": "IsValid", "type": "bool"},
            {"name": "ErrorMessage", "type": "string"}
        ]
    elif "calculate" in desc_lower or "math" in desc_lower:
        inputs = [
            {"name": "ValueA", "type": "float"},
            {"name": "ValueB", "type": "float"}
        ]
        outputs = [
            {"name": "Result", "type": "float"},
            {"name": "IsValid", "type": "bool"}
        ]
    elif "string" in desc_lower or "text" in desc_lower:
        inputs = [
            {"name": "InputText", "type": "string"}
        ]
        outputs = [
            {"name": "OutputText", "type": "string"}
        ]
    elif "number" in desc_lower or "count" in desc_lower:
        inputs = [
            {"name": "InputValue", "type": "int32"}
        ]
        outputs = [
            {"name": "OutputValue", "type": "int32"}
        ]
    else:
        # Generic parameters
        outputs = [
            {"name": "Result", "type": "string"},
            {"name": "Success", "type": "bool"}
        ]
    
    return inputs, outputs

# --- DYNAMIC NODE LOOKUP UTILITY ---
def find_blueprint_node(node_purpose, description=None):
    """
    Dynamically look up the correct Blueprint node type, function name, and class for a given purpose.
    For now, uses a static registry, but can be extended to query Unreal's Python API for all available nodes.
    Args:
        node_purpose: e.g. 'random_integer', 'print_string', 'set_variable'
        description: Optional, for more context
    Returns:
        Dict with node_type, function_name, function_class, etc.
    """
    # TODO: Replace this with a dynamic query to Unreal's Blueprint libraries via Python API
    registry = {
        'random_integer': {
            'node_type': 'K2Node_CallFunction',
            'function_name': 'RandomIntegerInRange',
            'function_class': 'KismetMathLibrary',
        },
        'print_string': {
            'node_type': 'K2Node_CallFunction',
            'function_name': 'PrintString',
            'function_class': 'KismetSystemLibrary',
        },
        'set_variable': {
            'node_type': 'K2Node_VariableSet',
        },
    }
    return registry.get(node_purpose, {})

# New approach: Create complete Blueprint graphs with connected nodes
class BlueprintGraphCreator:
    def __init__(self):
        self.node_registry = {}
        self.connection_registry = []
        
    def create_password_generator_blueprint(self, blueprint_path=None):
        """
        Create a complete password generator Blueprint using the unreal-mcp approach
        - Create all nodes at once
        - Connect them properly
        - Ensure proper pin connections
        """
        try:
            # If no path provided, create new Blueprint
            if not blueprint_path:
                blueprint_path = '/Game/GeneratedBlueprints/PasswordGenerator'
            
            print(f"Creating password generator Blueprint at: {blueprint_path}")
            
            # Create the Blueprint asset
            blueprint = self._create_blueprint_asset(blueprint_path)
            if not blueprint:
                return None
                
            # Create a function in the Blueprint
            function_graph = self._create_function(blueprint, "GeneratePassword")
            if not function_graph:
                return None
                
            # Create the complete node graph using batch creation
            success = self._create_password_generator_graph(function_graph)
            
            if success:
                # Compile and save the Blueprint
                unreal.BlueprintEditorUtils.compile_blueprint(blueprint)
                unreal.EditorAssetLibrary.save_asset(blueprint_path, False)
                print(f"Successfully created password generator Blueprint: {blueprint_path}")
                return blueprint_path
            else:
                print("Failed to create password generator graph")
                return None
                
        except Exception as e:
            print(f"Error creating password generator Blueprint: {str(e)}")
            return None
    
    def _create_blueprint_asset(self, blueprint_path):
        """Create a new Blueprint asset"""
        try:
            # Create Blueprint factory
            blueprint_factory = unreal.BlueprintFactory()
            blueprint_factory.parent_class = unreal.Actor
            
            # Create the asset
            asset_tools = unreal.AssetToolsHelpers.get_asset_tools()
            package_path = blueprint_path.rsplit('/', 1)[0]
            asset_name = blueprint_path.rsplit('/', 1)[1]
            
            blueprint = asset_tools.create_asset(
                asset_name,
                package_path,
                unreal.Blueprint,
                blueprint_factory
            )
            
            return blueprint
            
        except Exception as e:
            print(f"Error creating Blueprint asset: {str(e)}")
            return None
    
    def _create_function(self, blueprint, function_name):
        """Create a new function in the Blueprint"""
        try:
            # Create function graph
            function_graph = unreal.BlueprintEditorUtils.create_new_graph(
                blueprint,
                function_name,
                unreal.EdGraph,
                unreal.EdGraphSchema_K2
            )
            
            if function_graph:
                # Add to function graphs
                blueprint.function_graphs.append(function_graph)
                
                # Create function entry node
                entry_node = unreal.EdGraphNode()
                entry_node.node_pos_x = 0
                entry_node.node_pos_y = 0
                function_graph.add_node(entry_node, True, True)
                
                print(f"Created function: {function_name}")
                return function_graph
            
        except Exception as e:
            print(f"Error creating function: {str(e)}")
            
        return None
    
    def _create_password_generator_graph(self, function_graph):
        """Create the complete password generator node graph with connections"""
        try:
            # Define the complete node structure for password generation
            nodes_data = [
                {
                    "id": "entry",
                    "type": "FunctionEntry",
                    "pos": [0, 0],
                    "outputs": [{"name": "exec", "type": "exec"}]
                },
                {
                    "id": "make_password_chars",
                    "type": "MakeLiteralString", 
                    "pos": [200, -100],
                    "properties": {"value": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"},
                    "outputs": [{"name": "string", "type": "string"}]
                },
                {
                    "id": "make_empty_result",
                    "type": "MakeLiteralString",
                    "pos": [200, 0], 
                    "properties": {"value": ""},
                    "outputs": [{"name": "string", "type": "string"}]
                },
                {
                    "id": "string_length",
                    "type": "StringLength",
                    "pos": [400, -100],
                    "inputs": [{"name": "string", "type": "string"}],
                    "outputs": [{"name": "length", "type": "int"}]
                },
                {
                    "id": "for_loop",
                    "type": "ForLoop", 
                    "pos": [600, 0],
                    "inputs": [
                        {"name": "exec", "type": "exec"},
                        {"name": "first_index", "type": "int"},
                        {"name": "last_index", "type": "int"}
                    ],
                    "outputs": [
                        {"name": "exec", "type": "exec"},
                        {"name": "loop_body", "type": "exec"},
                        {"name": "index", "type": "int"},
                        {"name": "completed", "type": "exec"}
                    ]
                },
                {
                    "id": "random_int",
                    "type": "RandomIntegerInRange",
                    "pos": [800, 100],
                    "inputs": [
                        {"name": "min", "type": "int"},
                        {"name": "max", "type": "int"}
                    ],
                    "outputs": [{"name": "value", "type": "int"}]
                },
                {
                    "id": "get_char",
                    "type": "GetCharacterAtIndex",
                    "pos": [1000, 0],
                    "inputs": [
                        {"name": "string", "type": "string"},
                        {"name": "index", "type": "int"}
                    ],
                    "outputs": [{"name": "char", "type": "string"}]
                },
                {
                    "id": "append_char",
                    "type": "Append",
                    "pos": [1200, 0],
                    "inputs": [
                        {"name": "a", "type": "string"},
                        {"name": "b", "type": "string"}
                    ],
                    "outputs": [{"name": "result", "type": "string"}]
                },
                {
                    "id": "print_result",
                    "type": "PrintString",
                    "pos": [800, -200],
                    "inputs": [
                        {"name": "exec", "type": "exec"},
                        {"name": "string", "type": "string"}
                    ],
                    "outputs": [{"name": "exec", "type": "exec"}]
                },
                {
                    "id": "return_node",
                    "type": "FunctionResult",
                    "pos": [1000, -200],
                    "inputs": [{"name": "exec", "type": "exec"}]
                }
            ]
            
            # Define connections between nodes
            connections = [
                {"from": "entry", "from_pin": "exec", "to": "for_loop", "to_pin": "exec"},
                {"from": "make_password_chars", "from_pin": "string", "to": "string_length", "to_pin": "string"},
                {"from": "make_password_chars", "from_pin": "string", "to": "get_char", "to_pin": "string"},
                {"from": "string_length", "from_pin": "length", "to": "random_int", "to_pin": "max"},
                {"from": "for_loop", "from_pin": "loop_body", "to": "random_int", "to_pin": "exec"},
                {"from": "random_int", "from_pin": "value", "to": "get_char", "to_pin": "index"},
                {"from": "get_char", "from_pin": "char", "to": "append_char", "to_pin": "b"},
                {"from": "make_empty_result", "from_pin": "string", "to": "append_char", "to_pin": "a"},
                {"from": "for_loop", "from_pin": "completed", "to": "print_result", "to_pin": "exec"},
                {"from": "append_char", "from_pin": "result", "to": "print_result", "to_pin": "string"},
                {"from": "print_result", "from_pin": "exec", "to": "return_node", "to_pin": "exec"}
            ]
            
            # Create all nodes using bulk creation
            created_nodes = self._create_nodes_bulk(function_graph, nodes_data)
            if not created_nodes:
                return False
            
            # Connect all nodes
            success = self._connect_nodes_bulk(function_graph, connections, created_nodes)
            
            return success
            
        except Exception as e:
            print(f"Error creating password generator graph: {str(e)}")
            return False
    
    def _create_nodes_bulk(self, function_graph, nodes_data):
        """Create all nodes at once using the bulk creation approach"""
        try:
            # Prepare bulk creation data
            blueprint_path = function_graph.get_outer().get_path_name()
            function_guid = str(function_graph.graph_guid)
            
            # Convert our node data to the format expected by the C++ system
            bulk_nodes = []
            for node_data in nodes_data:
                bulk_node = {
                    "id": node_data["id"],
                    "node_type": node_data["type"],
                    "node_position": node_data["pos"],
                    "node_properties": node_data.get("properties", {})
                }
                bulk_nodes.append(bulk_node)
            
            # Call the C++ bulk creation method
            nodes_json = json.dumps(bulk_nodes)
            
            # Use the existing C++ implementation but with our improved approach
            result = unreal.GenBlueprintNodeCreator.add_nodes_bulk(
                blueprint_path,
                function_guid, 
                nodes_json
            )
            
            if result:
                # Parse the result to get node mappings
                created_nodes = {}
                try:
                    result_data = json.loads(result)
                    for item in result_data:
                        if "ref_id" in item and "node_guid" in item:
                            created_nodes[item["ref_id"]] = item["node_guid"]
                except:
                    print("Failed to parse bulk creation result")
                    return None
                
                print(f"Successfully created {len(created_nodes)} nodes")
                return created_nodes
            else:
                print("Bulk node creation failed")
                return None
                
        except Exception as e:
            print(f"Error in bulk node creation: {str(e)}")
            return None
    
    def _connect_nodes_bulk(self, function_graph, connections, created_nodes):
        """Connect all nodes using a bulk connection approach"""
        try:
            blueprint_path = function_graph.get_outer().get_path_name()
            function_guid = str(function_graph.graph_guid)
            
            # Convert connections to the format expected by C++
            connection_data = []
            for conn in connections:
                from_guid = created_nodes.get(conn["from"])
                to_guid = created_nodes.get(conn["to"])
                
                if from_guid and to_guid:
                    connection_data.append({
                        "from_node": from_guid,
                        "from_pin": conn["from_pin"],
                        "to_node": to_guid,
                        "to_pin": conn["to_pin"]
                    })
            
            # Use the new C++ bulk connection method
            connections_json = json.dumps(connection_data)
            result = unreal.GenBlueprintNodeCreator.connect_nodes_bulk(
                blueprint_path,
                function_guid,
                connections_json
            )
            
            if result:
                try:
                    result_data = json.loads(result)
                    success = result_data.get("success", False)
                    successful_connections = result_data.get("successful_connections", 0)
                    total_connections = result_data.get("total_connections", 0)
                    
                    print(f"Connected {successful_connections} out of {total_connections} connections")
                    return success
                except:
                    print("Failed to parse connection result")
                    return False
            else:
                print("Bulk connection failed")
                return False
            
        except Exception as e:
            print(f"Error connecting nodes: {str(e)}")
            return False

    def create_complete_password_generator(self, blueprint_path=None):
        """Create complete password generator using the new CreateCompleteGraph method"""
        try:
            # If no path provided, create new Blueprint
            if not blueprint_path:
                blueprint_path = '/Game/GeneratedBlueprints/PasswordGeneratorComplete'
            
            print(f"Creating complete password generator Blueprint at: {blueprint_path}")
            
            # Create the Blueprint asset
            blueprint = self._create_blueprint_asset(blueprint_path)
            if not blueprint:
                return None
                
            # Create a function in the Blueprint
            function_graph = self._create_function(blueprint, "GeneratePassword")
            if not function_graph:
                return None
            
            # Define the complete graph structure
            graph_definition = {
                "nodes": [
                    {
                        "id": "entry",
                        "node_type": "FunctionEntry",
                        "node_position": [0, 0],
                        "node_properties": {}
                    },
                    {
                        "id": "make_password_chars",
                        "node_type": "MakeLiteralString", 
                        "node_position": [200, -100],
                        "node_properties": {"string_value": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"}
                    },
                    {
                        "id": "make_empty_result",
                        "node_type": "MakeLiteralString",
                        "node_position": [200, 0], 
                        "node_properties": {"string_value": ""}
                    },
                    {
                        "id": "string_length",
                        "node_type": "StringLength",
                        "node_position": [400, -100],
                        "node_properties": {}
                    },
                    {
                        "id": "for_loop",
                        "node_type": "ForLoop", 
                        "node_position": [600, 0],
                        "node_properties": {}
                    },
                    {
                        "id": "random_int",
                        "node_type": "RandomIntegerInRange",
                        "node_position": [800, 100],
                        "node_properties": {}
                    },
                    {
                        "id": "get_char",
                        "node_type": "GetCharacterAtIndex",
                        "node_position": [1000, 0],
                        "node_properties": {}
                    },
                    {
                        "id": "append_char",
                        "node_type": "Append",
                        "node_position": [1200, 0],
                        "node_properties": {}
                    },
                    {
                        "id": "print_result",
                        "node_type": "PrintString",
                        "node_position": [800, -200],
                        "node_properties": {}
                    },
                    {
                        "id": "return_node",
                        "node_type": "FunctionResult",
                        "node_position": [1000, -200],
                        "node_properties": {}
                    }
                ],
                "connections": [
                    {"from": "entry", "from_pin": "exec", "to": "for_loop", "to_pin": "exec"},
                    {"from": "make_password_chars", "from_pin": "string", "to": "string_length", "to_pin": "string"},
                    {"from": "make_password_chars", "from_pin": "string", "to": "get_char", "to_pin": "string"},
                    {"from": "string_length", "from_pin": "length", "to": "random_int", "to_pin": "max"},
                    {"from": "for_loop", "from_pin": "loop_body", "to": "random_int", "to_pin": "exec"},
                    {"from": "random_int", "from_pin": "value", "to": "get_char", "to_pin": "index"},
                    {"from": "get_char", "from_pin": "char", "to": "append_char", "to_pin": "b"},
                    {"from": "make_empty_result", "from_pin": "string", "to": "append_char", "to_pin": "a"},
                    {"from": "for_loop", "from_pin": "completed", "to": "print_result", "to_pin": "exec"},
                    {"from": "append_char", "from_pin": "result", "to": "print_result", "to_pin": "string"},
                    {"from": "print_result", "from_pin": "exec", "to": "return_node", "to_pin": "exec"}
                ]
            }
            
            # Use the new CreateCompleteGraph method
            graph_json = json.dumps(graph_definition)
            result = unreal.GenBlueprintNodeCreator.create_complete_graph(
                blueprint_path,
                str(function_graph.graph_guid),
                graph_json
            )
            
            if result:
                try:
                    result_data = json.loads(result)
                    success = result_data.get("success", False)
                    
                    if success:
                        # Compile and save the Blueprint
                        unreal.BlueprintEditorUtils.compile_blueprint(blueprint)
                        unreal.EditorAssetLibrary.save_asset(blueprint_path, False)
                        
                        print(f"Successfully created complete password generator Blueprint: {blueprint_path}")
                        print(f"Result: {result}")
                        return blueprint_path
                    else:
                        print("Failed to create complete graph")
                        print(f"Result: {result}")
                        return None
                except:
                    print("Failed to parse complete graph result")
                    return None
            else:
                print("CreateCompleteGraph returned empty result")
                return None
                
        except Exception as e:
            print(f"Error creating complete password generator Blueprint: {str(e)}")
            return None

# Global instance
blueprint_creator = BlueprintGraphCreator()

def create_password_generator():
    """Create a complete password generator Blueprint using original approach"""
    return blueprint_creator.create_password_generator_blueprint()

def create_complete_password_generator():
    """Create a complete password generator Blueprint using new CreateCompleteGraph approach"""
    return blueprint_creator.create_complete_password_generator()

def create_blueprint_function(blueprint_name, function_name, parameters=None):
    """Create a Blueprint function with the given name and parameters"""
    try:
        # Get or create blueprint
        blueprint_path = f'/Game/GeneratedBlueprints/{blueprint_name}'
        
        # Use existing Blueprint loading logic from unreal
        existing_blueprint = unreal.EditorAssetLibrary.find_asset_data(blueprint_path)
        
        if existing_blueprint.is_valid():
            blueprint = unreal.EditorAssetLibrary.load_asset(blueprint_path)
        else:
            blueprint = blueprint_creator._create_blueprint_asset(blueprint_path)
            
        if not blueprint:
            return None
            
        # Create the function
        function_graph = blueprint_creator._create_function(blueprint, function_name)
        
        if function_graph:
            # Compile and save
            unreal.BlueprintEditorUtils.compile_blueprint(blueprint)
            unreal.EditorAssetLibrary.save_asset(blueprint_path, False)
            
            return {
                'blueprint_path': blueprint_path,
                'function_guid': str(function_graph.graph_guid),
                'function_name': function_name
            }
        
        return None
        
    except Exception as e:
        print(f"Error creating Blueprint function: {str(e)}")
        return None

def handle_get_unreal_context(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Handle a command to get current Unreal Engine context data - LOCAL IMPLEMENTATION
    Uses the C++ sync system to get current Blueprint, window, and asset context
    
    Args:
        command: The command dictionary (no specific arguments needed)
            
    Returns:
        Response dictionary with current UE context data
    """
    try:
        log.log_command("get_unreal_context", "Getting current Unreal Engine context")
        
        # Import the blueprint context handler to use the C++ sync system
        try:
            import blueprint_context_handler
            log.log_info("✅ Successfully imported blueprint_context_handler")
        except ImportError as e:
            log.log_error(f"Failed to import blueprint_context_handler: {e}")
            return {
                "success": False,
                "error": f"Could not import blueprint context handler: {str(e)}",
                "suggestion": "Ensure the blueprint_context_handler.py file is in the handlers directory"
            }
        
        # Get current Blueprint context using the C++ sync system
        try:
            context_result = blueprint_context_handler.handle_blueprint_context_request(
                "get_current_blueprint_context", {}
            )
            
            if context_result.get("success", False):
                log.log_info("✅ Successfully retrieved UE context via C++ sync")
                
                # Extract the context data
                context_data = context_result.get("context", {})
                
                # Get additional UE editor information
                editor_info = _get_editor_context_info()
                
                # Combine all context information
                full_context = {
                    "blueprint_context": context_data,
                    "editor_context": editor_info,
                    "timestamp": time.time(),
                    "sync_method": "cpp_asset_registry"
                }
                
                log.log_result("get_unreal_context", True, f"Retrieved context with {len(context_data.get('blueprints', []))} blueprints")
                return {
                    "success": True,
                    "message": "Current Unreal Engine context retrieved successfully",
                    "context": full_context,
                    "blueprint_count": len(context_data.get("blueprints", [])),
                    "current_blueprint": context_data.get("current_blueprint", {}),
                    "editor_info": editor_info
                }
            else:
                log.log_error(f"Failed to get Blueprint context: {context_result}")
                return {
                    "success": False,
                    "error": f"Blueprint context retrieval failed: {context_result.get('error', 'Unknown error')}",
                    "context_result": context_result
                }
                
        except Exception as e:
            log.log_error(f"Exception during context retrieval: {str(e)}")
            return {
                "success": False,
                "error": f"Exception getting UE context: {str(e)}"
            }
            
    except Exception as e:
        log.log_error(f"Error getting Unreal Engine context: {str(e)}", include_traceback=True)
        return {"success": False, "error": str(e)}


def _get_editor_context_info() -> Dict[str, Any]:
    """
    Get additional Unreal Editor context information
    """
    try:
        editor_info = {
            "current_level": None,
            "current_world": None,
            "open_assets": [],
            "editor_mode": "Unknown",
            "project_path": None
        }
        
        # Get current level
        try:
            current_level = unreal.EditorLevelLibrary.get_current_level()
            if current_level:
                editor_info["current_level"] = {
                    "name": current_level.get_name(),
                    "path": current_level.get_path_name(),
                    "actor_count": len(unreal.EditorLevelLibrary.get_all_level_actors())
                }
        except Exception as e:
            log.log_warning(f"Could not get current level: {e}")
        
        # Get current world
        try:
            current_world = unreal.EditorLevelLibrary.get_editor_world()
            if current_world:
                editor_info["current_world"] = {
                    "name": current_world.get_name(),
                    "path": current_world.get_path_name(),
                    "level_count": len(current_world.get_levels())
                }
        except Exception as e:
            log.log_warning(f"Could not get current world: {e}")
        
        # Get project path
        try:
            editor_info["project_path"] = unreal.Paths.project_dir()
        except Exception as e:
            log.log_warning(f"Could not get project path: {e}")
        
        # Try to get open assets (if available)
        try:
            asset_subsystem = unreal.get_editor_subsystem(unreal.AssetEditorSubsystem)
            if asset_subsystem and hasattr(asset_subsystem, "get_all_edited_assets"):
                edited_assets = asset_subsystem.get_all_edited_assets()
                for asset in edited_assets:
                    if asset:
                        editor_info["open_assets"].append({
                            "name": asset.get_name(),
                            "path": asset.get_path_name(),
                            "type": type(asset).__name__
                        })
        except Exception as e:
            log.log_warning(f"Could not get open assets: {e}")
        
        return editor_info
        
    except Exception as e:
        log.log_error(f"Error getting editor context: {e}")
        return {"error": str(e)}

def handle_analyze_graph(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze a Blueprint function graph and return all nodes and connections.
    Args:
        command: The command dictionary containing:
            - blueprint_path: Path to the Blueprint asset
            - function_id: ID of the function graph
    Returns:
        Dictionary with nodes, pins, and connections
    """
    try:
        blueprint_path = command.get("blueprint_path")
        function_id = command.get("function_id")
        if not blueprint_path or not function_id:
            log.log_error("Missing required parameters for analyze_graph")
            return {"success": False, "error": "Missing required parameters"}
        node_creator = unreal.GenBlueprintNodeCreator
        analysis_json = node_creator.analyze_graph(blueprint_path, function_id)
        import json
        analysis = json.loads(analysis_json)
        return {"success": True, "analysis": analysis}
    except Exception as e:
        log.log_error(f"Error analyzing graph: {str(e)}")
        return {"success": False, "error": str(e)}

def handle_get_focused_graph(command: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get the currently focused graph/tab in the Blueprint Editor.
    Returns:
        Dictionary with graph name, GUID, blueprint name, and path, or error info.
    """
    try:
        node_creator = unreal.GenBlueprintNodeCreator
        focused_json = node_creator.get_current_focused_graph()
        import json
        focused_info = json.loads(focused_json)
        return {"success": True, "focused_graph": focused_info}
    except Exception as e:
        log.log_error(f"Error getting focused graph: {str(e)}")
        return {"success": False, "error": str(e)}