#!/usr/bin/env python3

import socket
import json

def test_context():
    try:
        s = socket.socket()
        s.connect(('localhost', 9877))
        
        request = {"type": "get_blueprint_context_detailed"}
        s.send(json.dumps(request).encode())
        
        response = s.recv(4096).decode()
        print("Response:", response)
        
        s.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_context()
