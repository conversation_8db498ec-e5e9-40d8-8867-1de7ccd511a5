// Copyright (c) 2025 <PERSON><PERSON><PERSON>. All rights reserved.
// Licensed under the MIT License. See LICENSE file in the root directory of this
// source tree or http://opensource.org/licenses/MIT.

#include "MCP/GenBlueprintNodeCreator.h"
#include "K2Node_CallFunction.h"
#include "K2Node_ExecutionSequence.h"
#include "K2Node_IfThenElse.h"
#include "K2Node_SwitchEnum.h"
#include "K2Node_SwitchInteger.h"
#include "K2Node_SwitchString.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "EdGraphSchema_K2.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"
#include "Engine/Blueprint.h"
#include "BlueprintEditor.h"
#include "Editor.h"
#include "K2Node_ComponentBoundEvent.h"
#include "K2Node_Event.h"
#include "K2Node_FunctionEntry.h"
#include "K2Node_FunctionResult.h"
#include "K2Node_InputAction.h"
#include "K2Node_Literal.h"
#include "Components/ShapeComponent.h"
#include "Engine/SCS_Node.h"
#include "Kismet/KismetMathLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Kismet/KismetStringLibrary.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "UObject/UnrealTypePrivate.h"


TMap<FString, FString> UGenBlueprintNodeCreator::NodeTypeMap;
bool IsBlueprintDirty = false;


FString UGenBlueprintNodeCreator::AddNode(const FString& BlueprintPath, const FString& FunctionGuid,
                                          const FString& NodeType, float NodeX, float NodeY,
                                          const FString& PropertiesJson, bool bFinalizeChanges)
{
	UE_LOG(LogTemp, Log, TEXT("AddNode called: Blueprint=%s, Function=%s, NodeType=%s"), 
		*BlueprintPath, *FunctionGuid, *NodeType);

	UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *BlueprintPath);
	if (!Blueprint)
	{
		UE_LOG(LogTemp, Error, TEXT("Could not load blueprint at path: %s"), *BlueprintPath);
		return TEXT("");
	}

	UEdGraph* FunctionGraph = GetGraphFromFunctionId(Blueprint, FunctionGuid);
	if (!FunctionGraph)
	{
		UE_LOG(LogTemp, Error, TEXT("Could not find function graph with GUID: %s"), *FunctionGuid);
		return TEXT("");
	}

	if (bFinalizeChanges)
		IsBlueprintDirty = false;

	UK2Node* NewNode = nullptr;
	TArray<FString> Suggestions;

	UE_LOG(LogTemp, Log, TEXT("Attempting to create node of type: %s"), *NodeType);

	// Try to create the node using known node types first
	if (TryCreateKnownNodeType(FunctionGraph, NodeType, NewNode, PropertiesJson))
	{
		if (!NewNode)
		{
			UE_LOG(LogTemp, Error, TEXT("TryCreateKnownNodeType returned true but node is null for type: %s"), *NodeType);
			return TEXT("");
		}
		UE_LOG(LogTemp, Log, TEXT("Successfully created known node type: %s"), *NodeType);
	}
	else
	{
		// Try to create from function libraries
		FString Result = TryCreateNodeFromLibraries(FunctionGraph, NodeType, NewNode, Suggestions);
		if (!Result.IsEmpty() && Result.StartsWith(TEXT("SUGGESTIONS:")))
		{
			UE_LOG(LogTemp, Warning, TEXT("Node creation failed, returning suggestions: %s"), *Result);
			return Result; // Return suggestions directly to caller
		}
		else if (!NewNode)
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to create node type: %s"), *NodeType);
			return TEXT("");
		}
		UE_LOG(LogTemp, Log, TEXT("Successfully created library node type: %s"), *NodeType);
	}

	// Ensure we have a valid node at this point
	if (!NewNode)
	{
		UE_LOG(LogTemp, Error, TEXT("Node creation completely failed for type: %s"), *NodeType);
		return TEXT("");
	}

	// Set position before adding to graph
	NewNode->NodePosX = NodeX;
	NewNode->NodePosY = NodeY;

	// Generate a GUID if it doesn't have one
	if (!NewNode->NodeGuid.IsValid())
	{
		NewNode->NodeGuid = FGuid::NewGuid();
	}

	UE_LOG(LogTemp, Log, TEXT("Adding node to graph with GUID: %s"), *NewNode->NodeGuid.ToString());

	// Add the node to the graph
	FunctionGraph->AddNode(NewNode, true, true);

	// Allocate default pins - this is critical for proper node functionality
	NewNode->AllocateDefaultPins();
	UE_LOG(LogTemp, Log, TEXT("Allocated default pins for node, pin count: %d"), NewNode->Pins.Num());

	// Apply properties if provided
	if (!PropertiesJson.IsEmpty())
	{
		UE_LOG(LogTemp, Log, TEXT("Applying properties from JSON: %s"), *PropertiesJson);
		
		TSharedPtr<FJsonObject> JsonObject;
		TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(PropertiesJson);
		if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
		{
			for (auto& Prop : JsonObject->Values)
			{
				const FString& PropName = Prop.Key;
				TSharedPtr<FJsonValue> PropValue = Prop.Value;
				
				UE_LOG(LogTemp, Log, TEXT("Setting property: %s"), *PropName);
				
				UEdGraphPin* Pin = NewNode->FindPin(FName(*PropName));
				if (Pin)
				{
					if (PropValue->Type == EJson::String) 
					{
						Pin->DefaultValue = PropValue->AsString();
						UE_LOG(LogTemp, Log, TEXT("Set pin %s to string value: %s"), *PropName, *PropValue->AsString());
					}
					else if (PropValue->Type == EJson::Number)
					{
						Pin->DefaultValue = FString::SanitizeFloat(PropValue->AsNumber());
						UE_LOG(LogTemp, Log, TEXT("Set pin %s to number value: %f"), *PropName, PropValue->AsNumber());
					}
					else if (PropValue->Type == EJson::Boolean)
					{
						Pin->DefaultValue = PropValue->AsBool() ? TEXT("true") : TEXT("false");
						UE_LOG(LogTemp, Log, TEXT("Set pin %s to boolean value: %s"), *PropName, PropValue->AsBool() ? TEXT("true") : TEXT("false"));
					}
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("Could not find pin named: %s"), *PropName);
				}

				// Special handling for variable nodes
				if ((NodeType.Equals(TEXT("VariableGet"), ESearchCase::IgnoreCase) ||
						NodeType.Equals(TEXT("VariableSet"), ESearchCase::IgnoreCase)) &&
					PropName.Equals(TEXT("variable_name"), ESearchCase::IgnoreCase) && PropValue->Type == EJson::String)
				{
					FString VariableName = PropValue->AsString();
					if (!VariableName.IsEmpty())
					{
						FMemberReference VarRef;
						VarRef.SetSelfMember(FName(*VariableName));
						if (UK2Node_VariableGet* VarGet = Cast<UK2Node_VariableGet>(NewNode))
						{
							VarGet->VariableReference = VarRef;
							UE_LOG(LogTemp, Log, TEXT("Set variable reference for VariableGet: %s"), *VariableName);
						}
						else if (UK2Node_VariableSet* VarSet = Cast<UK2Node_VariableSet>(NewNode))
						{
							VarSet->VariableReference = VarRef;
							UE_LOG(LogTemp, Log, TEXT("Set variable reference for VariableSet: %s"), *VariableName);
						}
					}
				}
			}
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Failed to parse properties JSON: %s"), *PropertiesJson);
		}
	}

	// Reconstruct the node to ensure proper initialization
	UE_LOG(LogTemp, Log, TEXT("Reconstructing node to finalize setup"));
	NewNode->ReconstructNode();

	// Log final pin state
	UE_LOG(LogTemp, Log, TEXT("Final node setup - Pin count: %d"), NewNode->Pins.Num());
	for (int32 i = 0; i < NewNode->Pins.Num(); i++)
	{
		UEdGraphPin* Pin = NewNode->Pins[i];
		if (Pin)
		{
			UE_LOG(LogTemp, Log, TEXT("  Pin %d: %s (%s, %s)"), 
				i, 
				*Pin->PinName.ToString(),
				Pin->Direction == EGPD_Output ? TEXT("Output") : TEXT("Input"),
				*Pin->PinType.PinCategory.ToString());
		}
	}

	// Handle finalization if requested
	if (bFinalizeChanges)
	{
		if (GEditor)
		{
			UAssetEditorSubsystem* AssetEditorSubsystem = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>();
			if (AssetEditorSubsystem)
			{
				AssetEditorSubsystem->OpenEditorForAsset(Blueprint);
				if (FBlueprintEditor* BlueprintEditor = static_cast<FBlueprintEditor*>(AssetEditorSubsystem->
					FindEditorForAsset(Blueprint, false)))
					BlueprintEditor->OpenGraphAndBringToFront(FunctionGraph);
			}
		}
		if (IsBlueprintDirty)
		{
			Blueprint->Modify();
			FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);
		}
	}

	UE_LOG(LogTemp, Log, TEXT("Successfully added node of type %s to blueprint %s with GUID %s"), 
		*NodeType, *BlueprintPath, *NewNode->NodeGuid.ToString());
	
	return NewNode->NodeGuid.ToString();
}


FString UGenBlueprintNodeCreator::AddNodesBulk(const FString& BlueprintPath, const FString& FunctionGuid,
                                               const FString& NodesJson)
{
	UE_LOG(LogTemp, Error, TEXT("🚨 AddNodesBulk ENTRY POINT - Function called!"));
	UE_LOG(LogTemp, Error, TEXT("🚨 BlueprintPath: %s"), *BlueprintPath);
	UE_LOG(LogTemp, Error, TEXT("🚨 FunctionGuid: %s"), *FunctionGuid);
	UE_LOG(LogTemp, Error, TEXT("🚨 NodesJson length: %d"), NodesJson.Len());

	UE_LOG(LogTemp, Error, TEXT("🚨 About to load Blueprint..."));
	UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *BlueprintPath);
	if (!Blueprint)
	{
		UE_LOG(LogTemp, Error, TEXT("🚨 Could not load blueprint at path: %s"), *BlueprintPath);
		return TEXT("");
	}
	UE_LOG(LogTemp, Error, TEXT("🚨 Blueprint loaded successfully"));

	FGuid GraphGuid;
	if (!FGuid::Parse(FunctionGuid, GraphGuid))
	{
		UE_LOG(LogTemp, Error, TEXT("Invalid GUID format: %s"), *FunctionGuid);
		return TEXT("");
	}
	UE_LOG(LogTemp, Log, TEXT("GUID parsed successfully"));

	UEdGraph* FunctionGraph = nullptr;
	for (UEdGraph* Graph : Blueprint->UbergraphPages)
		if (Graph->GraphGuid == GraphGuid)
		{
			FunctionGraph = Graph;
			break;
		}
	if (!FunctionGraph)
		for (UEdGraph* Graph : Blueprint->FunctionGraphs)
			if (Graph->GraphGuid == GraphGuid)
			{
				FunctionGraph = Graph;
				break;
			}
	if (!FunctionGraph)
	{
		UE_LOG(LogTemp, Error, TEXT("Could not find function graph with GUID: %s"), *FunctionGuid);
		return TEXT("");
	}
	UE_LOG(LogTemp, Log, TEXT("Function graph found successfully"));

	IsBlueprintDirty = false;

	TArray<TSharedPtr<FJsonValue>> NodesArray;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(NodesJson);
	if (!FJsonSerializer::Deserialize(Reader, NodesArray))
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to parse nodes JSON"));
		return TEXT("");
	}
	UE_LOG(LogTemp, Log, TEXT("JSON parsed successfully, found %d nodes"), NodesArray.Num());

	TSharedPtr<FJsonObject> ResultsObject = MakeShareable(new FJsonObject);
	TArray<TSharedPtr<FJsonValue>> ResultsArray;

	for (auto& NodeValue : NodesArray)
	{
		TSharedPtr<FJsonObject> NodeObject = NodeValue->AsObject();
		if (!NodeObject.IsValid())
		{
			UE_LOG(LogTemp, Warning, TEXT("Invalid node object, skipping"));
			continue;
		}

		FString NodeType = NodeObject->GetStringField(TEXT("node_type"));
		UE_LOG(LogTemp, Log, TEXT("Processing node type: %s"), *NodeType);

		// Safely get node position with error checking
		double NodeX = 0.0;
		double NodeY = 0.0;
		if (NodeObject->HasField(TEXT("node_position")))
		{
			TArray<TSharedPtr<FJsonValue>> PositionArray = NodeObject->GetArrayField(TEXT("node_position"));
			NodeX = PositionArray.Num() > 0 ? PositionArray[0]->AsNumber() : 0.0;
			NodeY = PositionArray.Num() > 1 ? PositionArray[1]->AsNumber() : 0.0;
		}

		FString PropertiesJson;
		if (NodeObject->HasField(TEXT("node_properties")))
		{
			TSharedPtr<FJsonObject> PropertiesObject = NodeObject->GetObjectField(TEXT("node_properties"));
			if (PropertiesObject.IsValid())
			{
				TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&PropertiesJson);
				FJsonSerializer::Serialize(PropertiesObject.ToSharedRef(), Writer);
			}
		}

		FString NodeRefId;
		if (NodeObject->HasField(TEXT("ref_id"))) NodeRefId = NodeObject->GetStringField(TEXT("ref_id"));
		else if (NodeObject->HasField(TEXT("node_id"))) NodeRefId = NodeObject->GetStringField(TEXT("node_id"));
		else if (NodeObject->HasField(TEXT("id"))) NodeRefId = NodeObject->GetStringField(TEXT("id"));

		UE_LOG(LogTemp, Log, TEXT("About to call AddNode for type: %s at position (%f, %f)"), *NodeType, NodeX, NodeY);
		FString NodeGuid = AddNode(BlueprintPath, FunctionGuid, NodeType, NodeX, NodeY, PropertiesJson, false);
		UE_LOG(LogTemp, Log, TEXT("AddNode returned GUID: %s"), *NodeGuid);

		if (!NodeGuid.IsEmpty())
		{
			TSharedPtr<FJsonObject> ResultObject = MakeShareable(new FJsonObject);
			ResultObject->SetStringField(TEXT("node_guid"), NodeGuid);
			if (!NodeRefId.IsEmpty()) ResultObject->SetStringField(TEXT("ref_id"), NodeRefId);
			ResultsArray.Add(MakeShareable(new FJsonValueObject(ResultObject)));
			UE_LOG(LogTemp, Log, TEXT("Successfully added node result to array"));
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("AddNode returned empty GUID for node type: %s"), *NodeType);
		}
	}

	if (ResultsArray.Num() > 0)
	{
		if (GEditor)
		{
			UAssetEditorSubsystem* AssetEditorSubsystem = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>();
			if (AssetEditorSubsystem)
			{
				AssetEditorSubsystem->OpenEditorForAsset(Blueprint);
				if (FBlueprintEditor* BlueprintEditor = static_cast<FBlueprintEditor*>(AssetEditorSubsystem->
					FindEditorForAsset(Blueprint, false)))
					BlueprintEditor->OpenGraphAndBringToFront(FunctionGraph);
			}
		}

		if (IsBlueprintDirty)
		{
			Blueprint->Modify();
			FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);
		}
	}

	FString ResultsJson;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ResultsJson);
	FJsonSerializer::Serialize(ResultsArray, Writer);

	UE_LOG(LogTemp, Log, TEXT("Added %d nodes to blueprint %s"), ResultsArray.Num(), *BlueprintPath);
	return ResultsJson;
}


// Delete a specific node
bool UGenBlueprintNodeCreator::DeleteNode(const FString& BlueprintPath, const FString& FunctionGuid,
                                          const FString& NodeGuid)
{
	UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *BlueprintPath);
	if (!Blueprint)
	{
		UE_LOG(LogTemp, Error, TEXT("Could not load blueprint at path: %s"), *BlueprintPath);
		return false;
	}

	UEdGraph* FunctionGraph = nullptr;

	// Handle EventGraph special case
	if (FunctionGuid.Equals(TEXT("EventGraph"), ESearchCase::IgnoreCase))
	{
		if (Blueprint->UbergraphPages.Num() > 0)
			FunctionGraph = Blueprint->UbergraphPages[0];
	}
	else
	{
		// Parse GUID if not EventGraph
		FGuid GraphGuidObj;
		if (!FGuid::Parse(FunctionGuid, GraphGuidObj))
		{
			UE_LOG(LogTemp, Error, TEXT("Invalid graph GUID format: %s"), *FunctionGuid);
			return false;
		}

		FunctionGraph = FindGraphByGuid(Blueprint, GraphGuidObj);
	}

	if (!FunctionGraph)
	{
		UE_LOG(LogTemp, Error, TEXT("Could not find graph with ID: %s"), *FunctionGuid);
		return false;
	}

	// Parse the node GUID
	FGuid NodeGuidObj;
	if (!FGuid::Parse(NodeGuid, NodeGuidObj))
	{
		UE_LOG(LogTemp, Error, TEXT("Invalid node GUID format: %s"), *NodeGuid);
		return false;
	}

	// Log all nodes for debugging
	UE_LOG(LogTemp, Log, TEXT("Looking for node with GUID: %s in graph with %d nodes"),
	       *NodeGuidObj.ToString(), FunctionGraph->Nodes.Num());

	UEdGraphNode* NodeToDelete = nullptr;
	for (UEdGraphNode* Node : FunctionGraph->Nodes)
	{
		UE_LOG(LogTemp, Log, TEXT("  - Found node of type %s with GUID: %s"),
		       *Node->GetClass()->GetName(), *Node->NodeGuid.ToString());

		if (Node->NodeGuid == NodeGuidObj)
		{
			NodeToDelete = Node;
			UE_LOG(LogTemp, Log, TEXT("  - MATCHED!"));
			break;
		}
	}

	if (!NodeToDelete)
	{
		UE_LOG(LogTemp, Error, TEXT("No node found with GUID: %s"), *NodeGuidObj.ToString());
		return false;
	}

	// Actually remove the node and mark blueprint as modified
	FBlueprintEditorUtils::RemoveNode(Blueprint, NodeToDelete, true);
	FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);

	UE_LOG(LogTemp, Log, TEXT("Successfully deleted node with GUID: %s"), *NodeGuid);
	return true;
}

// Get all nodes in a graph with their positions
FString UGenBlueprintNodeCreator::GetAllNodesInGraph(const FString& BlueprintPath, const FString& FunctionGuid)
{
	UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *BlueprintPath);
	if (!Blueprint) return TEXT("");

	UEdGraph* FunctionGraph = nullptr;
	// In GetAllNodesInGraph:
	if (FunctionGuid.Equals(TEXT("EventGraph"), ESearchCase::IgnoreCase))
	{
		if (Blueprint->UbergraphPages.Num() > 0)
			FunctionGraph = Blueprint->UbergraphPages[0];
	}
	else
	{
		FGuid GraphGuid;
		if (!FGuid::Parse(FunctionGuid, GraphGuid)) return TEXT("");

		FunctionGraph = FindGraphByGuid(Blueprint, GraphGuid);
	}

	if (!FunctionGraph) return TEXT("");

	TArray<TSharedPtr<FJsonValue>> NodesArray;
	for (UEdGraphNode* Node : FunctionGraph->Nodes)
	{
		TSharedPtr<FJsonObject> NodeObject = MakeShareable(new FJsonObject);
		NodeObject->SetStringField(TEXT("node_guid"), Node->NodeGuid.ToString());
		NodeObject->SetStringField(TEXT("node_type"), Node->GetClass()->GetName());

		TArray<TSharedPtr<FJsonValue>> PositionArray;
		PositionArray.Add(MakeShareable(new FJsonValueNumber(Node->NodePosX)));
		PositionArray.Add(MakeShareable(new FJsonValueNumber(Node->NodePosY)));
		NodeObject->SetArrayField(TEXT("position"), PositionArray);

		NodesArray.Add(MakeShareable(new FJsonValueObject(NodeObject)));
	}

	FString ResultJson;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ResultJson);
	FJsonSerializer::Serialize(NodesArray, Writer);

	return ResultJson;
}

UEdGraph* UGenBlueprintNodeCreator::FindGraphByGuid(UBlueprint* Blueprint, const FGuid& GraphGuid)
{
	if (!Blueprint) return nullptr;

	// Look in UbergraphPages
	for (UEdGraph* Graph : Blueprint->UbergraphPages)
	{
		if (Graph && Graph->GraphGuid == GraphGuid)
		{
			return Graph;
		}
	}

	// Look in FunctionGraphs
	for (UEdGraph* Graph : Blueprint->FunctionGraphs)
	{
		if (Graph && Graph->GraphGuid == GraphGuid)
		{
			return Graph;
		}
	}

	// Look in MacroGraphs (if needed)
	for (UEdGraph* Graph : Blueprint->MacroGraphs)
	{
		if (Graph && Graph->GraphGuid == GraphGuid)
		{
			return Graph;
		}
	}

	return nullptr;
}


// Initialize the map with comprehensive node type mappings for automatic node creation
void UGenBlueprintNodeCreator::InitNodeTypeMap()
{
	if (NodeTypeMap.Num() > 0)
		return; // Already initialized

	UE_LOG(LogTemp, Log, TEXT("Initializing comprehensive node type map for automatic node creation"));

	// === CORE BLUEPRINT NODES ===
	NodeTypeMap.Add(TEXT("returnnode"), TEXT("K2Node_FunctionResult"));
	NodeTypeMap.Add(TEXT("functionresult"), TEXT("K2Node_FunctionResult"));
	NodeTypeMap.Add(TEXT("return"), TEXT("K2Node_FunctionResult"));
	NodeTypeMap.Add(TEXT("functionentry"), TEXT("K2Node_FunctionEntry"));
	NodeTypeMap.Add(TEXT("entry"), TEXT("K2Node_FunctionEntry"));

	// === MATH LIBRARY FUNCTIONS ===
	// Addition (UE5.6 uses DoubleDouble for float operations)
	NodeTypeMap.Add(TEXT("add"), TEXT("Add_DoubleDouble"));
	NodeTypeMap.Add(TEXT("floatplusfloat"), TEXT("Add_DoubleDouble"));
	NodeTypeMap.Add(TEXT("floatplus"), TEXT("Add_DoubleDouble"));
	NodeTypeMap.Add(TEXT("k2_addfloat"), TEXT("Add_DoubleDouble"));
	NodeTypeMap.Add(TEXT("add_floatfloat"), TEXT("Add_DoubleDouble"));
	NodeTypeMap.Add(TEXT("add_doubledouble"), TEXT("Add_DoubleDouble"));
	NodeTypeMap.Add(TEXT("add_intint"), TEXT("Add_IntInt"));
	NodeTypeMap.Add(TEXT("add_vectorvector"), TEXT("Add_VectorVector"));

	// Multiplication (UE5.6 uses DoubleDouble for float operations)
	NodeTypeMap.Add(TEXT("multiply"), TEXT("Multiply_DoubleDouble"));
	NodeTypeMap.Add(TEXT("multiply_floatfloat"), TEXT("Multiply_DoubleDouble"));
	NodeTypeMap.Add(TEXT("multiply_doubledouble"), TEXT("Multiply_DoubleDouble"));
	NodeTypeMap.Add(TEXT("multiply_intint"), TEXT("Multiply_IntInt"));
	NodeTypeMap.Add(TEXT("multiply_vectorfloat"), TEXT("Multiply_VectorFloat"));

	// Subtraction (UE5.6 uses DoubleDouble for float operations)
	NodeTypeMap.Add(TEXT("subtract"), TEXT("Subtract_DoubleDouble"));
	NodeTypeMap.Add(TEXT("subtract_floatfloat"), TEXT("Subtract_DoubleDouble"));
	NodeTypeMap.Add(TEXT("subtract_doubledouble"), TEXT("Subtract_DoubleDouble"));
	NodeTypeMap.Add(TEXT("subtract_intint"), TEXT("Subtract_IntInt"));
	NodeTypeMap.Add(TEXT("subtract_vectorvector"), TEXT("Subtract_VectorVector"));

	// Division (UE5.6 uses DoubleDouble for float operations)
	NodeTypeMap.Add(TEXT("divide"), TEXT("Divide_DoubleDouble"));
	NodeTypeMap.Add(TEXT("divide_floatfloat"), TEXT("Divide_DoubleDouble"));
	NodeTypeMap.Add(TEXT("divide_doubledouble"), TEXT("Divide_DoubleDouble"));
	NodeTypeMap.Add(TEXT("divide_intint"), TEXT("Divide_IntInt"));

	// Random functions
	NodeTypeMap.Add(TEXT("random"), TEXT("RandomFloat"));
	NodeTypeMap.Add(TEXT("randomfloat"), TEXT("RandomFloat"));
	NodeTypeMap.Add(TEXT("randominteger"), TEXT("RandomInteger"));
	NodeTypeMap.Add(TEXT("randomintegerinrange"), TEXT("RandomIntegerInRange"));
	NodeTypeMap.Add(TEXT("random integer in range"), TEXT("RandomIntegerInRange"));
	NodeTypeMap.Add(TEXT("random int in range"), TEXT("RandomIntegerInRange"));
	NodeTypeMap.Add(TEXT("random integer"), TEXT("RandomInteger"));
	NodeTypeMap.Add(TEXT("randomfloatinrange"), TEXT("RandomFloatInRange"));

	// Trigonometry
	NodeTypeMap.Add(TEXT("sin"), TEXT("Sin"));
	NodeTypeMap.Add(TEXT("cos"), TEXT("Cos"));
	NodeTypeMap.Add(TEXT("tan"), TEXT("Tan"));
	NodeTypeMap.Add(TEXT("asin"), TEXT("Asin"));
	NodeTypeMap.Add(TEXT("acos"), TEXT("Acos"));
	NodeTypeMap.Add(TEXT("atan"), TEXT("Atan"));

	// === STRING LIBRARY FUNCTIONS ===
	NodeTypeMap.Add(TEXT("printstring"), TEXT("PrintString"));
	NodeTypeMap.Add(TEXT("print string"), TEXT("PrintString"));
	NodeTypeMap.Add(TEXT("print"), TEXT("PrintString"));
	NodeTypeMap.Add(TEXT("makeliteralstring"), TEXT("MakeLiteralString"));
	NodeTypeMap.Add(TEXT("make literal string"), TEXT("MakeLiteralString"));
	NodeTypeMap.Add(TEXT("literalstring"), TEXT("MakeLiteralString"));
	NodeTypeMap.Add(TEXT("append"), TEXT("Append"));
	NodeTypeMap.Add(TEXT("concat"), TEXT("Append"));
	NodeTypeMap.Add(TEXT("concatstring"), TEXT("Append"));
	NodeTypeMap.Add(TEXT("concat string"), TEXT("Append"));
	NodeTypeMap.Add(TEXT("stringlength"), TEXT("StringLength"));
	NodeTypeMap.Add(TEXT("string length"), TEXT("StringLength"));
	NodeTypeMap.Add(TEXT("len"), TEXT("StringLength"));
	NodeTypeMap.Add(TEXT("getcharacteratindex"), TEXT("GetCharacterAtIndex"));
	NodeTypeMap.Add(TEXT("get character at index"), TEXT("GetCharacterAtIndex"));
	NodeTypeMap.Add(TEXT("getchar"), TEXT("GetCharacterAtIndex"));

	// === CONTROL FLOW ===
	NodeTypeMap.Add(TEXT("branch"), TEXT("Branch"));
	NodeTypeMap.Add(TEXT("if"), TEXT("Branch"));
	NodeTypeMap.Add(TEXT("ifthenelse"), TEXT("Branch"));
	NodeTypeMap.Add(TEXT("if then else"), TEXT("Branch"));
	NodeTypeMap.Add(TEXT("forloop"), TEXT("ForLoop"));
	NodeTypeMap.Add(TEXT("for loop"), TEXT("ForLoop"));
	NodeTypeMap.Add(TEXT("for"), TEXT("ForLoop"));
	NodeTypeMap.Add(TEXT("whileloop"), TEXT("WhileLoop"));
	NodeTypeMap.Add(TEXT("while loop"), TEXT("WhileLoop"));
	NodeTypeMap.Add(TEXT("while"), TEXT("WhileLoop"));
	NodeTypeMap.Add(TEXT("sequence"), TEXT("Sequence"));
	NodeTypeMap.Add(TEXT("delay"), TEXT("Delay"));

	// === VECTOR FUNCTIONS ===
	NodeTypeMap.Add(TEXT("makevector"), TEXT("MakeVector"));
	NodeTypeMap.Add(TEXT("make vector"), TEXT("MakeVector"));
	NodeTypeMap.Add(TEXT("vector"), TEXT("MakeVector"));
	NodeTypeMap.Add(TEXT("breakvector"), TEXT("BreakVector"));
	NodeTypeMap.Add(TEXT("break vector"), TEXT("BreakVector"));
	NodeTypeMap.Add(TEXT("addvector"), TEXT("Add_VectorVector"));
	NodeTypeMap.Add(TEXT("vectorlength"), TEXT("VectorLength"));
	NodeTypeMap.Add(TEXT("vector length"), TEXT("VectorLength"));
	NodeTypeMap.Add(TEXT("normalize"), TEXT("Normalize"));
	NodeTypeMap.Add(TEXT("normalizevector"), TEXT("Normalize"));

	// === ACTOR FUNCTIONS ===
	NodeTypeMap.Add(TEXT("getactorlocation"), TEXT("GetActorLocation"));
	NodeTypeMap.Add(TEXT("get actor location"), TEXT("GetActorLocation"));
	NodeTypeMap.Add(TEXT("getlocation"), TEXT("GetActorLocation"));
	NodeTypeMap.Add(TEXT("setactorlocation"), TEXT("SetActorLocation"));
	NodeTypeMap.Add(TEXT("set actor location"), TEXT("SetActorLocation"));
	NodeTypeMap.Add(TEXT("setlocation"), TEXT("SetActorLocation"));
	NodeTypeMap.Add(TEXT("getactorrotation"), TEXT("GetActorRotation"));
	NodeTypeMap.Add(TEXT("get actor rotation"), TEXT("GetActorRotation"));
	NodeTypeMap.Add(TEXT("getrotation"), TEXT("GetActorRotation"));
	NodeTypeMap.Add(TEXT("setactorrotation"), TEXT("SetActorRotation"));
	NodeTypeMap.Add(TEXT("set actor rotation"), TEXT("SetActorRotation"));
	NodeTypeMap.Add(TEXT("setrotation"), TEXT("SetActorRotation"));

	// === VARIABLE NODES ===
	NodeTypeMap.Add(TEXT("getvariable"), TEXT("VariableGet"));
	NodeTypeMap.Add(TEXT("get variable"), TEXT("VariableGet"));
	NodeTypeMap.Add(TEXT("variableget"), TEXT("VariableGet"));
	NodeTypeMap.Add(TEXT("setvariable"), TEXT("VariableSet"));
	NodeTypeMap.Add(TEXT("set variable"), TEXT("VariableSet"));
	NodeTypeMap.Add(TEXT("variableset"), TEXT("VariableSet"));

	// === EVENT NODES ===
	NodeTypeMap.Add(TEXT("eventbeginplay"), TEXT("EventBeginPlay"));
	NodeTypeMap.Add(TEXT("event begin play"), TEXT("EventBeginPlay"));
	NodeTypeMap.Add(TEXT("beginplay"), TEXT("EventBeginPlay"));
	NodeTypeMap.Add(TEXT("receivebeginplay"), TEXT("EventBeginPlay"));
	NodeTypeMap.Add(TEXT("eventtick"), TEXT("EventTick"));
	NodeTypeMap.Add(TEXT("event tick"), TEXT("EventTick"));
	NodeTypeMap.Add(TEXT("tick"), TEXT("EventTick"));
	NodeTypeMap.Add(TEXT("receivetick"), TEXT("EventTick"));

	// === INPUT NODES ===
	NodeTypeMap.Add(TEXT("inputaction"), TEXT("K2Node_InputAction"));
	NodeTypeMap.Add(TEXT("input action"), TEXT("K2Node_InputAction"));
	NodeTypeMap.Add(TEXT("input"), TEXT("K2Node_InputAction"));
	NodeTypeMap.Add(TEXT("actionevent"), TEXT("K2Node_InputAction"));
	NodeTypeMap.Add(TEXT("inputevent"), TEXT("K2Node_InputAction"));

	// === TIME FUNCTIONS ===
	NodeTypeMap.Add(TEXT("gettime"), TEXT("GetTimeSeconds"));
	NodeTypeMap.Add(TEXT("get time"), TEXT("GetTimeSeconds"));
	NodeTypeMap.Add(TEXT("gettimeseconds"), TEXT("GetTimeSeconds"));
	NodeTypeMap.Add(TEXT("get time seconds"), TEXT("GetTimeSeconds"));
	NodeTypeMap.Add(TEXT("time"), TEXT("GetTimeSeconds"));

	// === LIBRARY PREFIX MAPPINGS ===
	NodeTypeMap.Add(TEXT("kismetmathlibrary.add_floatfloat"), TEXT("Add_FloatFloat"));
	NodeTypeMap.Add(TEXT("kismetmathlibrary.multiply_floatfloat"), TEXT("Multiply_FloatFloat"));
	NodeTypeMap.Add(TEXT("kismetmathlibrary.randomintegerinrange"), TEXT("RandomIntegerInRange"));
	NodeTypeMap.Add(TEXT("kismetsystemlibrary.printstring"), TEXT("PrintString"));
	NodeTypeMap.Add(TEXT("kismetstringlibrary.append"), TEXT("Append"));

	// === K2 PREFIX MAPPINGS ===
	NodeTypeMap.Add(TEXT("k2_getactorlocation"), TEXT("GetActorLocation"));
	NodeTypeMap.Add(TEXT("k2_setactorlocation"), TEXT("SetActorLocation"));
	NodeTypeMap.Add(TEXT("k2_addfloat"), TEXT("Add_FloatFloat"));

	// === AI-DRIVEN NODE MAPPINGS ===
	// Enhanced mappings for AI-generated Blueprint functions
	NodeTypeMap.Add(TEXT("getplayercontroller"), TEXT("GetPlayerController"));
	NodeTypeMap.Add(TEXT("getplayerpawn"), TEXT("GetPlayerPawn"));
	NodeTypeMap.Add(TEXT("getgamemode"), TEXT("GetGameMode"));
	NodeTypeMap.Add(TEXT("getgamestate"), TEXT("GetGameState"));
	NodeTypeMap.Add(TEXT("getplayerstate"), TEXT("GetPlayerState"));
	NodeTypeMap.Add(TEXT("getworldsettings"), TEXT("GetWorldSettings"));
	NodeTypeMap.Add(TEXT("getactorbyclass"), TEXT("GetActorOfClass"));
	NodeTypeMap.Add(TEXT("getallactorsofclass"), TEXT("GetAllActorsOfClass"));
	NodeTypeMap.Add(TEXT("getactorsbytag"), TEXT("GetAllActorsWithTag"));
	NodeTypeMap.Add(TEXT("findactorbyname"), TEXT("FindActorByName"));

	// Physics and collision
	NodeTypeMap.Add(TEXT("getvelocity"), TEXT("GetVelocity"));
	NodeTypeMap.Add(TEXT("setvelocity"), TEXT("SetPhysicsLinearVelocity"));
	NodeTypeMap.Add(TEXT("addimpulseatposition"), TEXT("AddImpulseAtPosition"));
	NodeTypeMap.Add(TEXT("addforceatlocation"), TEXT("AddForceAtLocation"));
	NodeTypeMap.Add(TEXT("addtorque"), TEXT("AddTorque"));
	NodeTypeMap.Add(TEXT("setcollisionenabled"), TEXT("SetCollisionEnabled"));
	NodeTypeMap.Add(TEXT("setcollisionresponse"), TEXT("SetCollisionResponseToChannel"));
	NodeTypeMap.Add(TEXT("enablecollision"), TEXT("SetCollisionEnabled"));
	NodeTypeMap.Add(TEXT("disablecollision"), TEXT("SetCollisionEnabled"));

	// Object management
	NodeTypeMap.Add(TEXT("setvisibility"), TEXT("SetActorHiddenInGame"));
	NodeTypeMap.Add(TEXT("setenablecollision"), TEXT("SetActorEnableCollision"));
	NodeTypeMap.Add(TEXT("settickenabled"), TEXT("SetActorTickEnabled"));
	NodeTypeMap.Add(TEXT("setlifetime"), TEXT("SetLifeSpan"));
	NodeTypeMap.Add(TEXT("getlifetime"), TEXT("GetLifeSpan"));
	NodeTypeMap.Add(TEXT("isvalid"), TEXT("IsValid"));
	NodeTypeMap.Add(TEXT("isnull"), TEXT("IsNull"));
	NodeTypeMap.Add(TEXT("isnotequal"), TEXT("NotEqual_ObjectObject"));
	NodeTypeMap.Add(TEXT("isequal"), TEXT("EqualEqual_ObjectObject"));
	NodeTypeMap.Add(TEXT("cast"), TEXT("Cast"));
	NodeTypeMap.Add(TEXT("getclass"), TEXT("GetClass"));
	NodeTypeMap.Add(TEXT("getobjectname"), TEXT("GetObjectName"));
	NodeTypeMap.Add(TEXT("getdisplayname"), TEXT("GetDisplayName"));

	// Advanced data structures
	NodeTypeMap.Add(TEXT("makestruct"), TEXT("MakeStruct"));
	NodeTypeMap.Add(TEXT("breakstruct"), TEXT("BreakStruct"));
	NodeTypeMap.Add(TEXT("makearray"), TEXT("MakeArray"));
	NodeTypeMap.Add(TEXT("arrayget"), TEXT("Array_Get"));
	NodeTypeMap.Add(TEXT("arrayset"), TEXT("Array_Set"));
	NodeTypeMap.Add(TEXT("arrayappend"), TEXT("Array_Append"));
	NodeTypeMap.Add(TEXT("arraylength"), TEXT("Array_Length"));
	NodeTypeMap.Add(TEXT("arraycontains"), TEXT("Array_Contains"));
	NodeTypeMap.Add(TEXT("arrayfind"), TEXT("Array_Find"));
	NodeTypeMap.Add(TEXT("arrayremove"), TEXT("Array_Remove"));
	NodeTypeMap.Add(TEXT("arrayclear"), TEXT("Array_Clear"));

	// UI and widgets
	NodeTypeMap.Add(TEXT("createwidget"), TEXT("CreateWidget"));
	NodeTypeMap.Add(TEXT("addtoviewport"), TEXT("AddToViewport"));
	NodeTypeMap.Add(TEXT("removefromparent"), TEXT("RemoveFromParent"));
	NodeTypeMap.Add(TEXT("setwidgetvisibility"), TEXT("SetVisibility"));

	// Audio
	NodeTypeMap.Add(TEXT("playsound2d"), TEXT("PlaySound2D"));
	NodeTypeMap.Add(TEXT("playsoundatposition"), TEXT("PlaySoundAtLocation"));
	NodeTypeMap.Add(TEXT("setvolume"), TEXT("SetVolumeMultiplier"));

	// Animation
	NodeTypeMap.Add(TEXT("playanimation"), TEXT("PlayAnimation"));
	NodeTypeMap.Add(TEXT("stopanimation"), TEXT("StopAnimation"));
	NodeTypeMap.Add(TEXT("setanimationmode"), TEXT("SetAnimationMode"));

	// Networking
	NodeTypeMap.Add(TEXT("serverrpc"), TEXT("ServerRPC"));
	NodeTypeMap.Add(TEXT("clientrpc"), TEXT("ClientRPC"));
	NodeTypeMap.Add(TEXT("multicastrpc"), TEXT("MulticastRPC"));
	NodeTypeMap.Add(TEXT("isserver"), TEXT("IsServer"));
	NodeTypeMap.Add(TEXT("isclient"), TEXT("IsClient"));

	// Save/Load
	NodeTypeMap.Add(TEXT("savegametoslot"), TEXT("SaveGameToSlot"));
	NodeTypeMap.Add(TEXT("loadgamefromslot"), TEXT("LoadGameFromSlot"));
	NodeTypeMap.Add(TEXT("doesslotexist"), TEXT("DoesSaveGameExist"));
	NodeTypeMap.Add(TEXT("deleteslot"), TEXT("DeleteGameInSlot"));

	UE_LOG(LogTemp, Log, TEXT("AI-enhanced node type map initialized with %d mappings"), NodeTypeMap.Num());
}


bool UGenBlueprintNodeCreator::TryCreateKnownNodeType(UEdGraph* Graph, const FString& NodeType, UK2Node*& OutNode,
                                                      const FString& PropertiesJson)
{
	InitNodeTypeMap();
	FString ActualNodeType;
	FString LowerNodeType = NodeType.ToLower();

	UE_LOG(LogTemp, Log, TEXT("[NODE_CREATION] Attempting to create node type: '%s'"), *NodeType);

	// Use Find for cross-version compatibility
	if (const FString* FoundType = NodeTypeMap.Find(LowerNodeType))
	{
		ActualNodeType = *FoundType;
		UE_LOG(LogTemp, Log, TEXT("[NODE_CREATION] Mapped '%s' to '%s'"), *NodeType, *ActualNodeType);
	}
	else
	{
		ActualNodeType = NodeType;
		UE_LOG(LogTemp, Warning, TEXT("[NODE_CREATION] No mapping found for '%s', using original"), *NodeType);

		// Try some common variations if no direct mapping found
		FString CleanedType = NodeType;
		CleanedType = CleanedType.Replace(TEXT(" "), TEXT(""));
		CleanedType = CleanedType.Replace(TEXT("_"), TEXT(""));
		CleanedType = CleanedType.ToLower();

		if (const FString* FoundCleanedType = NodeTypeMap.Find(CleanedType))
		{
			ActualNodeType = *FoundCleanedType;
			UE_LOG(LogTemp, Log, TEXT("[NODE_CREATION] Found mapping for cleaned type '%s' -> '%s'"), *CleanedType, *ActualNodeType);
		}
	}

	if (ActualNodeType.Equals(TEXT("EventBeginPlay"), ESearchCase::IgnoreCase) ||
		ActualNodeType.Equals(TEXT("BeginPlay"), ESearchCase::IgnoreCase) ||
		ActualNodeType.Equals(TEXT("ReceiveBeginPlay"), ESearchCase::IgnoreCase))
	{
		UBlueprint* Blueprint = Cast<UBlueprint>(Graph->GetOuter());
		if (!Blueprint || Blueprint->UbergraphPages.Num() == 0)
		{
			UE_LOG(LogTemp, Error, TEXT("No valid Blueprint or UbergraphPages found for EventBeginPlay"));
			return false;
		}

		UEdGraph* DefaultEventGraph = Blueprint->UbergraphPages[0];
		// Ensure we're adding to the default EventGraph
		if (Graph != DefaultEventGraph)
		{
			UE_LOG(LogTemp, Warning,
			       TEXT("EventBeginPlay can only be added to the default EventGraph, not a custom function graph"));
			return false;
		}

		// Find and delete ALL existing BeginPlay nodes first
		TArray<UK2Node_Event*> NodesToDelete;
		for (UEdGraphNode* Node : DefaultEventGraph->Nodes)
		{
			if (UK2Node_Event* EventNode = Cast<UK2Node_Event>(Node))
			{
				if (EventNode->EventReference.GetMemberName().ToString().Equals(TEXT("ReceiveBeginPlay")))
				{
					NodesToDelete.Add(EventNode);
					UE_LOG(LogTemp, Warning, TEXT("Found BeginPlay node to delete with GUID %s"),
					       *EventNode->NodeGuid.ToString());
				}
			}
		}

		// Delete all found BeginPlay nodes
		for (UK2Node_Event* NodeToDelete : NodesToDelete)
		{
			UE_LOG(LogTemp, Warning, TEXT("Deleting BeginPlay node with GUID %s"),
			       *NodeToDelete->NodeGuid.ToString());
			FBlueprintEditorUtils::RemoveNode(Blueprint, NodeToDelete);
		}

		UK2Node_Event* NewEventNode = NewObject<UK2Node_Event>(DefaultEventGraph);
		NewEventNode->EventReference.SetExternalMember(FName(TEXT("ReceiveBeginPlay")), AActor::StaticClass());
		NewEventNode->bOverrideFunction = true;

		if (!NewEventNode)
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to create EventBeginPlay node using FKismetEditorUtilities"));
			return false;
		}

		OutNode = NewEventNode;
		UE_LOG(LogTemp, Log, TEXT("Created new EventBeginPlay node in default EventGraph with GUID %s"),
		       *NewEventNode->NodeGuid.ToString());

		IsBlueprintDirty = true;
		return true;
	}
	else if (ActualNodeType.Equals(TEXT("EventTick"), ESearchCase::IgnoreCase))
	{
		UBlueprint* Blueprint = Cast<UBlueprint>(Graph->GetOuter());
		if (!Blueprint || Blueprint->UbergraphPages.Num() == 0)
		{
			UE_LOG(LogTemp, Error, TEXT("No valid Blueprint or UbergraphPages found for EventTick"));
			return false;
		}

		UEdGraph* DefaultEventGraph = Blueprint->UbergraphPages[0];
		if (Graph != DefaultEventGraph)
		{
			UE_LOG(LogTemp, Warning,
			       TEXT("EventTick can only be added to the default EventGraph, not a custom function graph"));
			return false;
		}

		// Find and delete ALL existing Tick nodes first
		TArray<UK2Node_Event*> NodesToDelete;
		for (UEdGraphNode* Node : DefaultEventGraph->Nodes)
		{
			if (UK2Node_Event* EventNode = Cast<UK2Node_Event>(Node))
			{
				if (EventNode->EventReference.GetMemberName().ToString().Equals(TEXT("ReceiveTick")))
				{
					NodesToDelete.Add(EventNode);
					UE_LOG(LogTemp, Warning, TEXT("Found Tick node to delete with GUID %s"),
					       *EventNode->NodeGuid.ToString());
				}
			}
		}

		// Delete all found Tick nodes
		for (UK2Node_Event* NodeToDelete : NodesToDelete)
		{
			UE_LOG(LogTemp, Warning, TEXT("Deleting Tick node with GUID %s"),
			       *NodeToDelete->NodeGuid.ToString());
			FBlueprintEditorUtils::RemoveNode(Blueprint, NodeToDelete);
		}

		UK2Node_Event* NewEventNode = NewObject<UK2Node_Event>(DefaultEventGraph);
		NewEventNode->EventReference.SetExternalMember(FName(TEXT("ReceiveTick")), AActor::StaticClass());
		NewEventNode->bOverrideFunction = true;

		if (!NewEventNode)
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to create EventTick node"));
			return false;
		}

		OutNode = NewEventNode;
		UE_LOG(LogTemp, Log, TEXT("Created new EventTick node in default EventGraph with GUID %s"),
		       *NewEventNode->NodeGuid.ToString());

		IsBlueprintDirty = true;
		return true;
	}
	auto NodeTypeLower = ActualNodeType.ToLower();
	// Special handling for function entry nodes - find existing rather than create new
	if (NodeTypeLower.Contains(TEXT("functionentry")) || NodeTypeLower.Contains(TEXT("entrynode")))
	{
		for (UEdGraphNode* Node : Graph->Nodes)
		{
			UK2Node_FunctionEntry* EntryNode = Cast<UK2Node_FunctionEntry>(Node);
			if (EntryNode)
			{
				OutNode = EntryNode;
				return true;
			}
		}
		// Even If we don't find one, don't create a new one as it would be a duplicate
		return false;
	}

	// New handling for InputAction node
	if (ActualNodeType.Equals(TEXT("K2Node_InputAction"), ESearchCase::IgnoreCase))
	{
		UK2Node_InputAction* InputNode = NewObject<UK2Node_InputAction>(Graph);
		if (!PropertiesJson.IsEmpty())
		{
			TSharedPtr<FJsonObject> JsonObject;
			TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(PropertiesJson);
			if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
			{
				FString ActionName;
				if (JsonObject->TryGetStringField(TEXT("action_name"), ActionName) && !ActionName.IsEmpty())
				{
					InputNode->InputActionName = FName(*ActionName);
					UE_LOG(LogTemp, Log, TEXT("Created InputAction node for action '%s'"), *ActionName);
				}
				else
				{
					UE_LOG(LogTemp, Error, TEXT("InputAction node requires 'action_name' in PropertiesJson"));
					return false;
				}
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("Failed to parse PropertiesJson for InputAction node"));
				return false;
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("InputAction node requires PropertiesJson with 'action_name'"));
			return false;
		}
		OutNode = InputNode;
		IsBlueprintDirty = true;
		return true;
	}
	if (ActualNodeType.Equals(TEXT("Branch"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("IfThenElse"), ESearchCase::IgnoreCase))
	{
		OutNode = NewObject<UK2Node_IfThenElse>(Graph);
		IsBlueprintDirty = true;
		return true;
	}
	else if (ActualNodeType.Equals(TEXT("Sequence"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("ExecutionSequence"), ESearchCase::IgnoreCase))
	{
		OutNode = NewObject<UK2Node_ExecutionSequence>(Graph);
		IsBlueprintDirty = true;
		return true;
	}
	else if (ActualNodeType.Equals(TEXT("SwitchEnum"), ESearchCase::IgnoreCase))
	{
		OutNode = NewObject<UK2Node_SwitchEnum>(Graph);
		IsBlueprintDirty = true;
		return true;
	}
	else if (ActualNodeType.Equals(TEXT("SwitchInteger"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("SwitchInt"), ESearchCase::IgnoreCase))
	{
		OutNode = NewObject<UK2Node_SwitchInteger>(Graph);
		IsBlueprintDirty = true;
		return true;
	}
	else if (ActualNodeType.Equals(TEXT("SwitchString"), ESearchCase::IgnoreCase))
	{
		OutNode = NewObject<UK2Node_SwitchString>(Graph);
		IsBlueprintDirty = true;
		return true;
	}
	else if (ActualNodeType.Equals(TEXT("VariableGet"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("Getter"), ESearchCase::IgnoreCase))
	{
		UK2Node_VariableGet* VarGet = NewObject<UK2Node_VariableGet>(Graph);
		if (!PropertiesJson.IsEmpty())
		{
			TSharedPtr<FJsonObject> JsonObject;
			TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(PropertiesJson);
			if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
			{
				FString VarName;
				if (JsonObject->TryGetStringField(TEXT("VariableName"), VarName) && !VarName.IsEmpty())
				{
					FMemberReference VarRef;
					VarRef.SetSelfMember(FName(*VarName));
					VarGet->VariableReference = VarRef;
					VarGet->AllocateDefaultPins(); // Ensure pins are created after setting the reference
					OutNode = VarGet;
					IsBlueprintDirty = true;
					UE_LOG(LogTemp, Log, TEXT("Created VariableGet node for variable '%s'"), *VarName);
					return true;
				}
			}
		}
		UE_LOG(LogTemp, Error, TEXT("VariableGet requires 'VariableName' in PropertiesJson"));
		return false;
	}
	else if (ActualNodeType.Equals(TEXT("VariableSet"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("Setter"), ESearchCase::IgnoreCase))
	{
		UK2Node_VariableSet* VarSet = NewObject<UK2Node_VariableSet>(Graph);
		if (!PropertiesJson.IsEmpty())
		{
			TSharedPtr<FJsonObject> JsonObject;
			TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(PropertiesJson);
			if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
			{
				FString VarName;
				if (JsonObject->TryGetStringField(TEXT("VariableName"), VarName) && !VarName.IsEmpty())
				{
					FMemberReference VarRef;
					VarRef.SetSelfMember(FName(*VarName));
					VarSet->VariableReference = VarRef;
					VarSet->AllocateDefaultPins(); // Ensure pins are created after setting the reference

					// Optionally set a default value if provided
					FString Value;
					if (JsonObject->TryGetStringField(TEXT("Value"), Value))
					{
						UEdGraphPin* ValuePin = VarSet->FindPin(FName(TEXT("Value")));
						if (ValuePin)
						{
							ValuePin->DefaultValue = Value;
						}
					}

					OutNode = VarSet;
					IsBlueprintDirty = true;
					UE_LOG(LogTemp, Log, TEXT("Created VariableSet node for variable '%s'"), *VarName);
					return true;
				}
			}
		}
		UE_LOG(LogTemp, Error, TEXT("VariableSet requires 'VariableName' in PropertiesJson"));
		return false;
	}
	else if (ActualNodeType.Equals(TEXT("Multiply"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("Multiply_Float"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(TEXT("Multiply_FloatFloat"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(TEXT("Multiply_DoubleDouble"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetMathLibrary"), TEXT("Multiply_DoubleDouble"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("Add"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("Add_Float"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(TEXT("Add_FloatFloat"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(TEXT("Add_DoubleDouble"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetMathLibrary"), TEXT("Add_DoubleDouble"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("Add_IntInt"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("Add_Integer"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetMathLibrary"), TEXT("Add_IntInt"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("Subtract"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("Subtract_Float"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(TEXT("Subtract_DoubleDouble"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetMathLibrary"), TEXT("Subtract_DoubleDouble"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("Divide"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("Divide_Float"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(TEXT("Divide_DoubleDouble"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetMathLibrary"), TEXT("Divide_DoubleDouble"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("Print"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("PrintString"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetSystemLibrary"), TEXT("PrintString"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("MakeLiteralString"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("MakeString"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetStringLibrary"), TEXT("MakeLiteralString"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("RandomIntegerInRange"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("RandomInteger"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetMathLibrary"), TEXT("RandomIntegerInRange"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("Append"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("AppendString"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetStringLibrary"), TEXT("Concat_StrStr"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("ForLoop"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("For Loop"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetSystemLibrary"), TEXT("ForLoop"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("StringLength"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("String Length"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetStringLibrary"), TEXT("Len"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("GetCharacterAtIndex"), ESearchCase::IgnoreCase) || ActualNodeType.Equals(
		TEXT("Get Character At Index"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetStringLibrary"), TEXT("GetCharacterAsString"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("Delay"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetSystemLibrary"), TEXT("Delay"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("GetActorLocation"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("Actor"), TEXT("K2_GetActorLocation"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("SetActorLocation"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("Actor"), TEXT("K2_SetActorLocation"), OutNode);
	}
	// Add conversion nodes
	else if (NodeType.Equals(TEXT("FloatToDouble"), ESearchCase::IgnoreCase) ||
		NodeType.Equals(TEXT("Conv_FloatToDouble"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetMathLibrary"), TEXT("Conv_FloatToDouble"), OutNode);
	}
	else if (NodeType.Equals(TEXT("FloatToInt"), ESearchCase::IgnoreCase) ||
		NodeType.Equals(TEXT("Conv_FloatToInteger"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetMathLibrary"), TEXT("Conv_FloatToInteger"), OutNode);
	}
	else if (NodeType.Equals(TEXT("IntToFloat"), ESearchCase::IgnoreCase) ||
		NodeType.Equals(TEXT("Conv_IntToFloat"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetMathLibrary"), TEXT("Conv_IntToFloat"), OutNode);
	}
	else if (NodeType.Equals(TEXT("DoubleToFloat"), ESearchCase::IgnoreCase) ||
		NodeType.Equals(TEXT("Conv_DoubleToFloat"), ESearchCase::IgnoreCase))
	{
		return CreateMathFunctionNode(Graph, TEXT("KismetMathLibrary"), TEXT("Conv_DoubleToFloat"), OutNode);
	}
	else if (ActualNodeType.Equals(TEXT("K2Node_FunctionResult"), ESearchCase::IgnoreCase) ||
		ActualNodeType.Equals(TEXT("FunctionResult"), ESearchCase::IgnoreCase) ||
		ActualNodeType.Equals(TEXT("ReturnNode"), ESearchCase::IgnoreCase))
	{
		// Create function result node (return node)
		UK2Node_FunctionResult* ResultNode = NewObject<UK2Node_FunctionResult>(Graph);
		if (ResultNode)
		{
			Graph->AddNode(ResultNode, true, true);

			// Configure output pins based on function outputs
			if (!PropertiesJson.IsEmpty())
			{
				TSharedPtr<FJsonObject> JsonObject;
				TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(PropertiesJson);
				if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
				{
					// Check if outputs are specified
					if (JsonObject->HasField(TEXT("outputs")))
					{
						const TArray<TSharedPtr<FJsonValue>>* OutputsArray;
						if (JsonObject->TryGetArrayField(TEXT("outputs"), OutputsArray))
						{
							UE_LOG(LogTemp, Log, TEXT("Configuring return node with %d outputs"), OutputsArray->Num());

							// Configure the function result node with proper output pins
							for (int32 i = 0; i < OutputsArray->Num(); i++)
							{
								const TSharedPtr<FJsonObject> OutputObj = (*OutputsArray)[i]->AsObject();
								if (OutputObj.IsValid())
								{
									FString OutputName = OutputObj->GetStringField(TEXT("name"));
									FString OutputType = OutputObj->GetStringField(TEXT("type"));
									UE_LOG(LogTemp, Log, TEXT("Adding output pin: %s (%s)"), *OutputName, *OutputType);
								}
							}
						}
					}
				}
			}

			ResultNode->AllocateDefaultPins();
			OutNode = ResultNode;
			IsBlueprintDirty = true;
			UE_LOG(LogTemp, Log, TEXT("Created K2Node_FunctionResult node with configured outputs"));
			return true;
		}
		return false;
	}

	// FIXED for UE 5.6+ compatibility
#if ENGINE_MAJOR_VERSION >= 5 && ENGINE_MINOR_VERSION >= 6
	// UE 5.6+ - ANY_PACKAGE has been deprecated, use nullptr instead
	UClass* NodeClass = FindObject<UClass>(nullptr, *(TEXT("UK2Node_") + ActualNodeType));
	if (!NodeClass || !NodeClass->IsChildOf(UK2Node::StaticClass()))
		NodeClass = FindObject<UClass>(nullptr, *ActualNodeType);
#else
	// UE 5.5 and earlier - use ANY_PACKAGE
	UClass* NodeClass = FindObject<UClass>(ANY_PACKAGE, *(TEXT("UK2Node_") + ActualNodeType));
	if (!NodeClass || !NodeClass->IsChildOf(UK2Node::StaticClass()))
		NodeClass = FindObject<UClass>(ANY_PACKAGE, *ActualNodeType);
#endif
	if (NodeClass && NodeClass->IsChildOf(UK2Node::StaticClass()))
	{
		OutNode = NewObject<UK2Node>(Graph, NodeClass);
		IsBlueprintDirty = true;
		return OutNode != nullptr;
	}

	return false;
}

UEdGraph* UGenBlueprintNodeCreator::GetGraphFromFunctionId(UBlueprint* Blueprint, const FString& FunctionGuid)
{
	if (FunctionGuid.Equals(TEXT("EventGraph"), ESearchCase::IgnoreCase) ||
		FunctionGuid.Equals(TEXT("Event Graph"), ESearchCase::IgnoreCase))
	{
		if (Blueprint->UbergraphPages.Num() > 0)
		{
			UEdGraph* EventGraph = Blueprint->UbergraphPages[0]; // Typically the first Ubergraph page
			UE_LOG(LogTemp, Log, TEXT("Resolved 'EventGraph' to GUID %s"), *EventGraph->GraphGuid.ToString());
			return EventGraph;
		}
		UE_LOG(LogTemp, Error, TEXT("No Event Graph found in Blueprint"));
		return nullptr;
	}

	FGuid GraphGuid;
	if (FGuid::Parse(FunctionGuid, GraphGuid))
	{
		for (UEdGraph* Graph : Blueprint->UbergraphPages)
			if (Graph->GraphGuid == GraphGuid) return Graph;
		for (UEdGraph* Graph : Blueprint->FunctionGraphs)
			if (Graph->GraphGuid == GraphGuid) return Graph;
	}
	UE_LOG(LogTemp, Error, TEXT("Could not resolve function ID %s to a graph"), *FunctionGuid);
	return nullptr;
}

FString UGenBlueprintNodeCreator::TryCreateNodeFromLibraries(UEdGraph* Graph, const FString& NodeType,
                                                             UK2Node*& OutNode,
                                                             TArray<FString>& OutSuggestions)
{
	static const TArray<FString> CommonLibraries = {
		TEXT("KismetMathLibrary"), TEXT("KismetSystemLibrary"), TEXT("KismetStringLibrary"),
		TEXT("KismetArrayLibrary"), TEXT("KismetTextLibrary"), TEXT("GameplayStatics"),
		TEXT("BlueprintFunctionLibrary"), TEXT("Actor"), TEXT("Pawn"), TEXT("Character")
	};

	struct FFunctionMatch
	{
		FString LibraryName;
		FString FunctionName;
		FString DisplayName;
		int32 Score;
		UFunction* Function;
		UClass* Class;
	};

	TArray<FFunctionMatch> Matches;
	const FString NodeTypeLower = NodeType.ToLower();

	auto SplitName = [](const FString& Name) -> TArray<FString>
	{
		TArray<FString> Parts;
		Name.ParseIntoArray(Parts, TEXT("_"));
		if (Parts.Num() == 1)
		{
			FString Temp = Name;
			Parts.Empty();
			for (int32 i = 0; i < Temp.Len(); ++i)
			{
				if (i > 0 && FChar::IsUpper(Temp[i]))
				{
					Parts.Add(Temp.Mid(0, i));
					Temp = Temp.Mid(i);
					i = 0;
				}
			}
			if (!Temp.IsEmpty()) Parts.Add(Temp);
		}
		return Parts;
	};

	TArray<FString> NodeTypeParts = SplitName(NodeTypeLower);

	for (const FString& LibraryName : CommonLibraries)
	{
		// FIXED for UE 5.6+ compatibility
#if ENGINE_MAJOR_VERSION >= 5 && ENGINE_MINOR_VERSION >= 6
		// UE 5.6+ - ANY_PACKAGE has been deprecated, use nullptr instead
		UClass* LibClass = FindObject<UClass>(nullptr, *LibraryName);
#else
		// UE 5.5 and earlier - use ANY_PACKAGE
		UClass* LibClass = FindObject<UClass>(ANY_PACKAGE, *LibraryName);
#endif
		if (!LibClass) continue;

		for (TFieldIterator<UFunction> FuncIt(LibClass); FuncIt; ++FuncIt)
		{
			UFunction* Function = *FuncIt;
			FString FuncName = Function->GetName();
			FString FuncNameLower = FuncName.ToLower();

			if (Function->HasMetaData(TEXT("DeprecatedFunction")) || Function->HasMetaData(
				TEXT("BlueprintInternalUseOnly")))
				continue;

			int32 Score = 0;
			if (FuncNameLower == NodeTypeLower) Score = 120; // Exact match
			else if (FuncNameLower.Contains(NodeTypeLower)) Score = 80; // Substring match

			TArray<FString> FuncParts = SplitName(FuncNameLower);
			for (const FString& Part : NodeTypeParts)
				if (FuncParts.Contains(Part)) Score += 20;
			if (FuncNameLower.StartsWith(NodeTypeLower)) Score += 10;
			if (FuncNameLower.Len() > NodeTypeLower.Len() * 2) Score -= 15;

			if (Score > 0)
			{
				FFunctionMatch Match;
				Match.LibraryName = LibraryName;
				Match.FunctionName = FuncName;
				Match.DisplayName = FString::Printf(TEXT("%s.%s"), *LibraryName, *FuncName);
				Match.Score = Score;
				Match.Function = Function;
				Match.Class = LibClass;
				Matches.Add(Match);
			}
		}
	}

	if (Matches.Num() == 0) return TEXT("");

	Matches.Sort([](const FFunctionMatch& A, const FFunctionMatch& B) { return A.Score > B.Score; });
	for (const FFunctionMatch& Match : Matches) OutSuggestions.Add(Match.DisplayName);

	const int32 ScoreThreshold = 80; // Raised threshold for better precision
	if (Matches[0].Score >= ScoreThreshold)
	{
		const FFunctionMatch& BestMatch = Matches[0];
		UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(Graph);
		IsBlueprintDirty = true;
		if (FunctionNode)
		{
			FunctionNode->FunctionReference.SetExternalMember(BestMatch.Function->GetFName(), BestMatch.Class);
			OutNode = FunctionNode;
			OutSuggestions.Empty(); // Clear suggestions on success
			return BestMatch.DisplayName;
		}
		return TEXT("");
	}

	while (OutSuggestions.Num() > 10) OutSuggestions.RemoveAt(OutSuggestions.Num() - 1); // More suggestions
	FString SuggestionStr = FString::Join(OutSuggestions, TEXT(", "));
	return TEXT("SUGGESTIONS:") + SuggestionStr;
}

bool UGenBlueprintNodeCreator::CreateMathFunctionNode(UEdGraph* Graph, const FString& ClassName,
                                                      const FString& FunctionName,
                                                      UK2Node*& OutNode)
{
	UK2Node_CallFunction* FunctionNode = NewObject<UK2Node_CallFunction>(Graph);
	if (FunctionNode)
	{
		UClass* Class = nullptr;
		// Fix common function name mappings for UE5.6
		FString MutableFunctionName = FunctionName;

		// Try multiple methods to find the class
		if (ClassName == TEXT("KismetMathLibrary"))
		{
			Class = UKismetMathLibrary::StaticClass();
			if (FunctionName == TEXT("Add_FloatFloat"))
			{
				MutableFunctionName = TEXT("Add_float_float");  // Try lowercase version first
				UE_LOG(LogTemp, Warning, TEXT("Remapping Add_FloatFloat to Add_float_float"));
			}
			else if (FunctionName == TEXT("Add_IntInt"))
			{
				MutableFunctionName = TEXT("Add_int_int");  // Try lowercase version
				UE_LOG(LogTemp, Warning, TEXT("Remapping Add_IntInt to Add_int_int"));
			}
		}
		else if (ClassName == TEXT("KismetSystemLibrary"))
		{
			Class = UKismetSystemLibrary::StaticClass();
		}
		else if (ClassName == TEXT("KismetStringLibrary"))
		{
			Class = UKismetStringLibrary::StaticClass();
		}
		else
		{
			// Fallback: try to find by name
#if ENGINE_MAJOR_VERSION >= 5 && ENGINE_MINOR_VERSION >= 6
			Class = FindObject<UClass>(nullptr, *ClassName);
#else
			Class = FindObject<UClass>(ANY_PACKAGE, *ClassName);
#endif
		}

		if (Class)
		{
			UFunction* Function = Class->FindFunctionByName(*MutableFunctionName);
			if (Function)
			{
				FunctionNode->FunctionReference.SetExternalMember(Function->GetFName(), Class);
				Graph->AddNode(FunctionNode, true, true);
				FunctionNode->AllocateDefaultPins();
				OutNode = FunctionNode;
				IsBlueprintDirty = true;
				UE_LOG(LogTemp, Log, TEXT("Successfully created math function node: %s::%s"), *ClassName, *MutableFunctionName);
				return true;
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("Function not found: %s::%s"), *ClassName, *MutableFunctionName);

				// Try fallback for Add_FloatFloat -> Add_DoubleDouble
				if (FunctionName == TEXT("Add_FloatFloat") && MutableFunctionName == TEXT("Add_float_float"))
				{
					UE_LOG(LogTemp, Warning, TEXT("Trying fallback: Add_FloatFloat -> Add_DoubleDouble"));
					MutableFunctionName = TEXT("Add_DoubleDouble");
					UFunction* FallbackFunction = Class->FindFunctionByName(*MutableFunctionName);
					if (FallbackFunction)
					{
						FunctionNode->FunctionReference.SetExternalMember(FallbackFunction->GetFName(), Class);
						Graph->AddNode(FunctionNode, true, true);
						FunctionNode->AllocateDefaultPins();
						OutNode = FunctionNode;
						IsBlueprintDirty = true;
						UE_LOG(LogTemp, Log, TEXT("Successfully created fallback math function node: %s::%s"), *ClassName, *MutableFunctionName);
						return true;
					}
					else
					{
						UE_LOG(LogTemp, Warning, TEXT("Fallback function also not found: %s::%s"), *ClassName, *MutableFunctionName);
					}
				}

				// Debug: List all available functions in KismetMathLibrary
				if (ClassName == TEXT("KismetMathLibrary") && Class)
				{
					UE_LOG(LogTemp, Warning, TEXT("Available functions in KismetMathLibrary:"));
					for (TFieldIterator<UFunction> FuncIt(Class); FuncIt; ++FuncIt)
					{
						UFunction* Func = *FuncIt;
						if (Func && Func->GetName().Contains(TEXT("Add")))
						{
							UE_LOG(LogTemp, Warning, TEXT("  - %s"), *Func->GetName());
						}
					}
				}
			}
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Class not found: %s"), *ClassName);
		}
	}
	return false;
}

FString UGenBlueprintNodeCreator::GetNodeSuggestions(const FString& NodeType)
{
	static const TArray<FString> CommonLibraries = {
		TEXT("KismetMathLibrary"), TEXT("KismetSystemLibrary"), TEXT("KismetStringLibrary"),
		TEXT("KismetArrayLibrary"), TEXT("KismetTextLibrary"), TEXT("GameplayStatics"),
		TEXT("BlueprintFunctionLibrary"), TEXT("Actor"), TEXT("Pawn"), TEXT("Character")
	};

	struct FFunctionMatch
	{
		FString LibraryName;
		FString FunctionName;
		FString DisplayName;
		int32 Score;
	};

	TArray<FFunctionMatch> Matches;
	const FString NodeTypeLower = NodeType.ToLower();

	auto SplitName = [](const FString& Name) -> TArray<FString>
	{
		TArray<FString> Parts;
		Name.ParseIntoArray(Parts, TEXT("_"));
		if (Parts.Num() == 1)
		{
			FString Temp = Name;
			Parts.Empty();
			for (int32 i = 0; i < Temp.Len(); ++i)
			{
				if (i > 0 && FChar::IsUpper(Temp[i]))
				{
					Parts.Add(Temp.Mid(0, i));
					Temp = Temp.Mid(i);
					i = 0;
				}
			}
			if (!Temp.IsEmpty()) Parts.Add(Temp);
		}
		return Parts;
	};

	TArray<FString> NodeTypeParts = SplitName(NodeTypeLower);

	for (const FString& LibraryName : CommonLibraries)
	{
		// FIXED for UE 5.6+ compatibility
#if ENGINE_MAJOR_VERSION >= 5 && ENGINE_MINOR_VERSION >= 6
		// UE 5.6+ - ANY_PACKAGE has been deprecated, use nullptr instead
		UClass* LibClass = FindObject<UClass>(nullptr, *LibraryName);
#else
		// UE 5.5 and earlier - use ANY_PACKAGE
		UClass* LibClass = FindObject<UClass>(ANY_PACKAGE, *LibraryName);
#endif
		if (!LibClass) continue;

		for (TFieldIterator<UFunction> FuncIt(LibClass); FuncIt; ++FuncIt)
		{
			UFunction* Function = *FuncIt;
			FString FuncName = Function->GetName();
			FString FuncNameLower = FuncName.ToLower();

			if (Function->HasMetaData(TEXT("DeprecatedFunction")) || Function->HasMetaData(
				TEXT("BlueprintInternalUseOnly")))
				continue;

			int32 Score = 0;
			if (FuncNameLower == NodeTypeLower) Score = 100;
			else if (FuncNameLower.Contains(NodeTypeLower)) Score = 50;

			TArray<FString> FuncParts = SplitName(FuncNameLower);
			for (const FString& Part : NodeTypeParts)
				if (FuncParts.Contains(Part)) Score += 10;

			if (FuncNameLower.StartsWith(NodeTypeLower)) Score += 5;
			if (FuncNameLower.Len() > NodeTypeLower.Len() * 2) Score -= 10;

			if (Score > 0)
			{
				FFunctionMatch Match;
				Match.LibraryName = LibraryName;
				Match.FunctionName = FuncName;
				Match.DisplayName = FString::Printf(TEXT("%s.%s"), *LibraryName, *FuncName);
				Match.Score = Score;
				Matches.Add(Match);
			}
		}
	}

	if (Matches.Num() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("No suggestions found for node type: %s"), *NodeType);
		return TEXT("");
	}

	Matches.Sort([](const FFunctionMatch& A, const FFunctionMatch& B) { return A.Score > B.Score; });
	TArray<FString> Suggestions;
	for (const FFunctionMatch& Match : Matches) Suggestions.Add(Match.DisplayName);
	while (Suggestions.Num() > 5) Suggestions.RemoveAt(Suggestions.Num() - 1);

	FString SuggestionStr = FString::Join(Suggestions, TEXT(", "));
	UE_LOG(LogTemp, Log, TEXT("Suggestions for %s: %s"), *NodeType, *SuggestionStr);
	return TEXT("SUGGESTIONS:") + SuggestionStr;
}

// NEW IMPLEMENTATION: Bulk connection system following unreal-mcp approach
FString UGenBlueprintNodeCreator::ConnectNodesBulk(const FString& BlueprintPath, const FString& FunctionGuid, const FString& ConnectionsJson)
{
	UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *BlueprintPath);
	if (!Blueprint)
	{
		UE_LOG(LogTemp, Error, TEXT("Could not load blueprint at path: %s"), *BlueprintPath);
		return TEXT("");
	}

	UEdGraph* FunctionGraph = GetGraphFromFunctionId(Blueprint, FunctionGuid);
	if (!FunctionGraph)
	{
		UE_LOG(LogTemp, Error, TEXT("Could not find function graph with GUID: %s"), *FunctionGuid);
		return TEXT("");
	}

	// Parse connections JSON
	TArray<TSharedPtr<FJsonValue>> ConnectionsArray;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ConnectionsJson);
	if (!FJsonSerializer::Deserialize(Reader, ConnectionsArray))
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to parse connections JSON"));
		return TEXT("");
	}

	int32 SuccessfulConnections = 0;
	int32 TotalConnections = ConnectionsArray.Num();

	// Process each connection
	for (auto& ConnectionValue : ConnectionsArray)
	{
		TSharedPtr<FJsonObject> ConnectionObject = ConnectionValue->AsObject();
		if (!ConnectionObject.IsValid()) continue;

		FString FromNodeGuid = ConnectionObject->GetStringField(TEXT("from_node"));
		FString FromPinName = ConnectionObject->GetStringField(TEXT("from_pin"));
		FString ToNodeGuid = ConnectionObject->GetStringField(TEXT("to_node"));
		FString ToPinName = ConnectionObject->GetStringField(TEXT("to_pin"));

		// Find the nodes
		UEdGraphNode* FromNode = FindNodeByGuid(FunctionGraph, FromNodeGuid);
		UEdGraphNode* ToNode = FindNodeByGuid(FunctionGraph, ToNodeGuid);

		if (!FromNode || !ToNode)
		{
			UE_LOG(LogTemp, Warning, TEXT("Could not find nodes for connection: %s -> %s"), *FromNodeGuid, *ToNodeGuid);
			continue;
		}

		// Find the pins
		UEdGraphPin* OutputPin = FindNodePin(FromNode, FromPinName, true);
		UEdGraphPin* InputPin = FindNodePin(ToNode, ToPinName, false);

		if (!OutputPin || !InputPin)
		{
			UE_LOG(LogTemp, Warning, TEXT("Could not find pins for connection: %s.%s -> %s.%s"), 
				*FromNode->GetNodeTitle(ENodeTitleType::FullTitle).ToString(), *FromPinName,
				*ToNode->GetNodeTitle(ENodeTitleType::FullTitle).ToString(), *ToPinName);
			continue;
		}

		// Make the connection
		if (ConnectPins(OutputPin, InputPin))
		{
			SuccessfulConnections++;
			UE_LOG(LogTemp, Log, TEXT("Successfully connected %s.%s -> %s.%s"), 
				*FromNode->GetNodeTitle(ENodeTitleType::FullTitle).ToString(), *FromPinName,
				*ToNode->GetNodeTitle(ENodeTitleType::FullTitle).ToString(), *ToPinName);
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Failed to connect %s.%s -> %s.%s"), 
				*FromNode->GetNodeTitle(ENodeTitleType::FullTitle).ToString(), *FromPinName,
				*ToNode->GetNodeTitle(ENodeTitleType::FullTitle).ToString(), *ToPinName);
		}
	}

	// Mark blueprint as modified if any connections were made
	if (SuccessfulConnections > 0)
	{
		Blueprint->Modify();
		FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);
		
		// Open the Blueprint editor
		if (GEditor)
		{
			UAssetEditorSubsystem* AssetEditorSubsystem = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>();
			if (AssetEditorSubsystem)
			{
				AssetEditorSubsystem->OpenEditorForAsset(Blueprint);
				if (FBlueprintEditor* BlueprintEditor = static_cast<FBlueprintEditor*>(AssetEditorSubsystem->FindEditorForAsset(Blueprint, false)))
				{
					BlueprintEditor->OpenGraphAndBringToFront(FunctionGraph);
				}
			}
		}
	}

	// Return result
	TSharedPtr<FJsonObject> ResultObject = MakeShareable(new FJsonObject);
	ResultObject->SetNumberField(TEXT("total_connections"), TotalConnections);
	ResultObject->SetNumberField(TEXT("successful_connections"), SuccessfulConnections);
	ResultObject->SetBoolField(TEXT("success"), SuccessfulConnections > 0);

	FString ResultJson;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ResultJson);
	FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);

	UE_LOG(LogTemp, Log, TEXT("Connected %d out of %d node connections"), SuccessfulConnections, TotalConnections);
	return ResultJson;
}

FString UGenBlueprintNodeCreator::CreateCompleteGraph(const FString& BlueprintPath, const FString& FunctionGuid, const FString& GraphDefinitionJson)
{
	// Parse graph definition
	TSharedPtr<FJsonObject> GraphDefinition;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(GraphDefinitionJson);
	if (!FJsonSerializer::Deserialize(Reader, GraphDefinition) || !GraphDefinition.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to parse graph definition JSON"));
		return TEXT("");
	}

	// Extract nodes and connections
	TArray<TSharedPtr<FJsonValue>> NodesArray = GraphDefinition->GetArrayField(TEXT("nodes"));
	TArray<TSharedPtr<FJsonValue>> ConnectionsArray = GraphDefinition->GetArrayField(TEXT("connections"));

	// First, create all nodes
	FString NodesJson;
	TSharedRef<TJsonWriter<>> NodesWriter = TJsonWriterFactory<>::Create(&NodesJson);
	FJsonSerializer::Serialize(NodesArray, NodesWriter);

	FString NodesResult = AddNodesBulk(BlueprintPath, FunctionGuid, NodesJson);
	if (NodesResult.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to create nodes"));
		return TEXT("");
	}

	// Parse the nodes result to get node mappings
	TSharedPtr<FJsonObject> NodesResultObject;
	TSharedRef<TJsonReader<>> NodesResultReader = TJsonReaderFactory<>::Create(NodesResult);
	TArray<TSharedPtr<FJsonValue>> CreatedNodesArray;
	if (FJsonSerializer::Deserialize(NodesResultReader, CreatedNodesArray))
	{
		// Convert connections to use the actual node GUIDs
		TMap<FString, FString> NodeIdToGuidMap;
		for (auto& NodeValue : CreatedNodesArray)
		{
			TSharedPtr<FJsonObject> NodeObject = NodeValue->AsObject();
			if (NodeObject.IsValid())
			{
				FString RefId = NodeObject->GetStringField(TEXT("ref_id"));
				FString NodeGuid = NodeObject->GetStringField(TEXT("node_guid"));
				if (!RefId.IsEmpty() && !NodeGuid.IsEmpty())
				{
					NodeIdToGuidMap.Add(RefId, NodeGuid);
				}
			}
		}

		// Update connections with actual node GUIDs
		TArray<TSharedPtr<FJsonValue>> UpdatedConnectionsArray;
		for (auto& ConnectionValue : ConnectionsArray)
		{
			TSharedPtr<FJsonObject> ConnectionObject = ConnectionValue->AsObject();
			if (ConnectionObject.IsValid())
			{
				FString FromId = ConnectionObject->GetStringField(TEXT("from"));
				FString ToId = ConnectionObject->GetStringField(TEXT("to"));
				
				if (FString* FromGuid = NodeIdToGuidMap.Find(FromId))
				{
					ConnectionObject->SetStringField(TEXT("from_node"), *FromGuid);
				}
				if (FString* ToGuid = NodeIdToGuidMap.Find(ToId))
				{
					ConnectionObject->SetStringField(TEXT("to_node"), *ToGuid);
				}
				
				UpdatedConnectionsArray.Add(MakeShareable(new FJsonValueObject(ConnectionObject)));
			}
		}

		// Create connections
		FString ConnectionsJson;
		TSharedRef<TJsonWriter<>> ConnectionsWriter = TJsonWriterFactory<>::Create(&ConnectionsJson);
		FJsonSerializer::Serialize(UpdatedConnectionsArray, ConnectionsWriter);

		FString ConnectionsResult = ConnectNodesBulk(BlueprintPath, FunctionGuid, ConnectionsJson);
		
		// Combine results
		TSharedPtr<FJsonObject> FinalResult = MakeShareable(new FJsonObject);
		FinalResult->SetStringField(TEXT("nodes_result"), NodesResult);
		FinalResult->SetStringField(TEXT("connections_result"), ConnectionsResult);
		FinalResult->SetBoolField(TEXT("success"), !ConnectionsResult.IsEmpty());

		FString FinalResultJson;
		TSharedRef<TJsonWriter<>> FinalWriter = TJsonWriterFactory<>::Create(&FinalResultJson);
		FJsonSerializer::Serialize(FinalResult.ToSharedRef(), FinalWriter);

		return FinalResultJson;
	}

	return TEXT("");
}

// Helper method implementations
bool UGenBlueprintNodeCreator::ConnectPins(UEdGraphPin* OutputPin, UEdGraphPin* InputPin)
{
	if (!OutputPin || !InputPin)
	{
		UE_LOG(LogTemp, Error, TEXT("ConnectPins: Null pin provided"));
		return false;
	}

	UE_LOG(LogTemp, Log, TEXT("Attempting to connect %s.%s -> %s.%s"), 
		*OutputPin->GetOwningNode()->GetNodeTitle(ENodeTitleType::FullTitle).ToString(), 
		*OutputPin->PinName.ToString(),
		*InputPin->GetOwningNode()->GetNodeTitle(ENodeTitleType::FullTitle).ToString(), 
		*InputPin->PinName.ToString());

	// Check if the connection is valid
	const UEdGraphSchema* Schema = OutputPin->GetSchema();
	if (!Schema)
	{
		UE_LOG(LogTemp, Error, TEXT("ConnectPins: No schema found"));
		return false;
	}

	FPinConnectionResponse ConnectionResponse = Schema->CanCreateConnection(OutputPin, InputPin);
	// In UE 5.6, CONNECT_RESPONSE_MAKE (0) means the connection can be made
	if (ConnectionResponse.Response != 0) // 0 = CONNECT_RESPONSE_MAKE
	{
		UE_LOG(LogTemp, Warning, TEXT("Cannot create connection between pins: %s (Response: %d)"), 
			*ConnectionResponse.Message.ToString(), ConnectionResponse.Response);
		return false;
	}

	// Break existing connections on the input pin if it's not an execution pin
	if (InputPin->PinType.PinCategory != UEdGraphSchema_K2::PC_Exec)
	{
		if (InputPin->LinkedTo.Num() > 0)
		{
			UE_LOG(LogTemp, Log, TEXT("Breaking existing connections on input pin"));
			InputPin->BreakAllPinLinks();
		}
	}

	// Make the connection (MakeLinkTo returns void in UE 5.6)
	OutputPin->MakeLinkTo(InputPin);
	
	// Verify the connection was made
	bool bConnectionMade = OutputPin->LinkedTo.Contains(InputPin);
	if (bConnectionMade)
	{
		UE_LOG(LogTemp, Log, TEXT("Successfully connected pins"));
		
		// Reconstruct the node to update its appearance
		OutputPin->GetOwningNode()->ReconstructNode();
		InputPin->GetOwningNode()->ReconstructNode();
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to create connection - link not established"));
	}
	
	return bConnectionMade;
}

UEdGraphPin* UGenBlueprintNodeCreator::FindNodePin(UEdGraphNode* Node, const FString& PinName, bool bIsOutput)
{
	if (!Node)
	{
		UE_LOG(LogTemp, Error, TEXT("FindNodePin: Null node provided"));
		return nullptr;
	}

	UE_LOG(LogTemp, Log, TEXT("Looking for pin '%s' on node '%s' (Output: %s)"), 
		*PinName, 
		*Node->GetNodeTitle(ENodeTitleType::FullTitle).ToString(),
		bIsOutput ? TEXT("true") : TEXT("false"));

	// Log all available pins for debugging
	UE_LOG(LogTemp, Log, TEXT("Available pins on node:"));
	for (UEdGraphPin* Pin : Node->Pins)
	{
		if (Pin)
		{
			UE_LOG(LogTemp, Log, TEXT("  - %s (%s, %s)"), 
				*Pin->PinName.ToString(),
				Pin->Direction == EGPD_Output ? TEXT("Output") : TEXT("Input"),
				*Pin->PinType.PinCategory.ToString());
		}
	}

	// First pass: Exact match
	for (UEdGraphPin* Pin : Node->Pins)
	{
		if (Pin && Pin->PinName.ToString().Equals(PinName, ESearchCase::IgnoreCase))
		{
			bool bPinIsOutput = (Pin->Direction == EGPD_Output);
			if (bPinIsOutput == bIsOutput)
			{
				UE_LOG(LogTemp, Log, TEXT("Found exact match pin: %s"), *Pin->PinName.ToString());
				return Pin;
			}
		}
	}

	// Second pass: Common aliases and variations
	TMap<FString, TArray<FString>> PinAliases;
	
	// Execution pins
	PinAliases.Add(TEXT("exec"), {TEXT("execute"), TEXT("then"), TEXT("out"), TEXT("")});
	PinAliases.Add(TEXT("execute"), {TEXT("exec"), TEXT("then"), TEXT("out")});
	PinAliases.Add(TEXT("then"), {TEXT("exec"), TEXT("execute"), TEXT("out")});
	
	// Common data pins
	PinAliases.Add(TEXT("result"), {TEXT("return value"), TEXT("output"), TEXT("out")});
	PinAliases.Add(TEXT("return value"), {TEXT("result"), TEXT("output"), TEXT("out")});
	PinAliases.Add(TEXT("output"), {TEXT("result"), TEXT("return value"), TEXT("out")});
	PinAliases.Add(TEXT("out"), {TEXT("output"), TEXT("result"), TEXT("return value")});
	
	// Input variations
	PinAliases.Add(TEXT("input"), {TEXT("in"), TEXT("value"), TEXT("a")});
	PinAliases.Add(TEXT("in"), {TEXT("input"), TEXT("value")});
	PinAliases.Add(TEXT("value"), {TEXT("input"), TEXT("in")});
	
	// Math function specific
	PinAliases.Add(TEXT("a"), {TEXT("input"), TEXT("value"), TEXT("x")});
	PinAliases.Add(TEXT("b"), {TEXT("input"), TEXT("value"), TEXT("y")});
	PinAliases.Add(TEXT("x"), {TEXT("a"), TEXT("input")});
	PinAliases.Add(TEXT("y"), {TEXT("b"), TEXT("input")});
	
	// String function specific
	PinAliases.Add(TEXT("string"), {TEXT("value"), TEXT("text"), TEXT("input")});
	PinAliases.Add(TEXT("text"), {TEXT("string"), TEXT("value")});
	
	// Array/index specific
	PinAliases.Add(TEXT("index"), {TEXT("i"), TEXT("position")});
	PinAliases.Add(TEXT("array"), {TEXT("target array"), TEXT("list")});

	FString PinNameLower = PinName.ToLower();
	
	// Try aliases
	if (TArray<FString>* Aliases = PinAliases.Find(PinNameLower))
	{
		for (const FString& Alias : *Aliases)
		{
			for (UEdGraphPin* Pin : Node->Pins)
			{
				if (Pin && Pin->PinName.ToString().Equals(Alias, ESearchCase::IgnoreCase))
				{
					bool bPinIsOutput = (Pin->Direction == EGPD_Output);
					if (bPinIsOutput == bIsOutput)
					{
						UE_LOG(LogTemp, Log, TEXT("Found alias match pin: %s (alias for %s)"), 
							*Pin->PinName.ToString(), *PinName);
						return Pin;
					}
				}
			}
		}
	}

	// Third pass: Partial/substring matching for complex pin names
	for (UEdGraphPin* Pin : Node->Pins)
	{
		if (Pin)
		{
			FString ActualPinName = Pin->PinName.ToString().ToLower();
			bool bPinIsOutput = (Pin->Direction == EGPD_Output);
			
			if (bPinIsOutput == bIsOutput)
			{
				// Check if the requested pin name is contained in the actual pin name
				if (ActualPinName.Contains(PinNameLower) || PinNameLower.Contains(ActualPinName))
				{
					UE_LOG(LogTemp, Log, TEXT("Found substring match pin: %s (matches %s)"), 
						*Pin->PinName.ToString(), *PinName);
					return Pin;
				}
			}
		}
	}

	// Fourth pass: For execution pins, try to find any execution pin in the right direction
	if (PinNameLower == TEXT("exec") || PinNameLower == TEXT("execute") || PinNameLower == TEXT("then"))
	{
		for (UEdGraphPin* Pin : Node->Pins)
		{
			if (Pin && Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Exec)
			{
				bool bPinIsOutput = (Pin->Direction == EGPD_Output);
				if (bPinIsOutput == bIsOutput)
				{
					UE_LOG(LogTemp, Log, TEXT("Found execution pin: %s"), *Pin->PinName.ToString());
					return Pin;
				}
			}
		}
	}

	// Fifth pass: For simple cases, try to find the first pin of the right direction and type
	if (bIsOutput)
	{
		// For output pins, prefer exec pins first, then any data pin
		for (UEdGraphPin* Pin : Node->Pins)
		{
			if (Pin && Pin->Direction == EGPD_Output)
			{
				if (Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Exec)
				{
					UE_LOG(LogTemp, Log, TEXT("Found default output exec pin: %s"), *Pin->PinName.ToString());
					return Pin;
				}
			}
		}
		// If no exec pin, find first output data pin
		for (UEdGraphPin* Pin : Node->Pins)
		{
			if (Pin && Pin->Direction == EGPD_Output && Pin->PinType.PinCategory != UEdGraphSchema_K2::PC_Exec)
			{
				UE_LOG(LogTemp, Log, TEXT("Found default output data pin: %s"), *Pin->PinName.ToString());
				return Pin;
			}
		}
	}
	else
	{
		// For input pins, prefer exec pins first, then any data pin
		for (UEdGraphPin* Pin : Node->Pins)
		{
			if (Pin && Pin->Direction == EGPD_Input)
			{
				if (Pin->PinType.PinCategory == UEdGraphSchema_K2::PC_Exec)
				{
					UE_LOG(LogTemp, Log, TEXT("Found default input exec pin: %s"), *Pin->PinName.ToString());
					return Pin;
				}
			}
		}
		// If no exec pin, find first input data pin
		for (UEdGraphPin* Pin : Node->Pins)
		{
			if (Pin && Pin->Direction == EGPD_Input && Pin->PinType.PinCategory != UEdGraphSchema_K2::PC_Exec)
			{
				UE_LOG(LogTemp, Log, TEXT("Found default input data pin: %s"), *Pin->PinName.ToString());
				return Pin;
			}
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("Could not find any suitable pin '%s' on node '%s'"), 
		*PinName, *Node->GetNodeTitle(ENodeTitleType::FullTitle).ToString());
	return nullptr;
}

UEdGraphNode* UGenBlueprintNodeCreator::FindNodeByGuid(UEdGraph* Graph, const FString& NodeGuid)
{
	if (!Graph)
	{
		return nullptr;
	}

	FGuid SearchGuid;
	if (!FGuid::Parse(NodeGuid, SearchGuid))
	{
		UE_LOG(LogTemp, Error, TEXT("Invalid node GUID format: %s"), *NodeGuid);
		return nullptr;
	}

	for (UEdGraphNode* Node : Graph->Nodes)
	{
		if (Node && Node->NodeGuid == SearchGuid)
		{
			return Node;
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("Could not find node with GUID: %s"), *NodeGuid);
	return nullptr;
}

FString UGenBlueprintNodeCreator::GetNodePins(const FString& BlueprintPath, const FString& FunctionGuid, const FString& NodeGuid)
{
	TSharedPtr<FJsonObject> ResultObject = MakeShareable(new FJsonObject);
	TArray<TSharedPtr<FJsonValue>> InputsArray;
	TArray<TSharedPtr<FJsonValue>> OutputsArray;

	UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *BlueprintPath);
	if (!Blueprint)
	{
		ResultObject->SetStringField(TEXT("error"), FString::Printf(TEXT("Could not load blueprint at path: %s"), *BlueprintPath));
		FString Output;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&Output);
		FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);
		return Output;
	}

	FGuid GraphGuid;
	if (!FGuid::Parse(FunctionGuid, GraphGuid))
	{
		ResultObject->SetStringField(TEXT("error"), FString::Printf(TEXT("Invalid function GUID: %s"), *FunctionGuid));
		FString Output;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&Output);
		FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);
		return Output;
	}

	UEdGraph* FunctionGraph = nullptr;
	for (UEdGraph* Graph : Blueprint->UbergraphPages)
		if (Graph->GraphGuid == GraphGuid)
		{
			FunctionGraph = Graph;
			break;
		}
	if (!FunctionGraph)
		for (UEdGraph* Graph : Blueprint->FunctionGraphs)
			if (Graph->GraphGuid == GraphGuid)
			{
				FunctionGraph = Graph;
				break;
			}
	if (!FunctionGraph)
	{
		ResultObject->SetStringField(TEXT("error"), FString::Printf(TEXT("Could not find function graph with GUID: %s"), *FunctionGuid));
		FString Output;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&Output);
		FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);
		return Output;
	}

	UEdGraphNode* TargetNode = nullptr;
	for (UEdGraphNode* Node : FunctionGraph->Nodes)
	{
		if (Node && Node->NodeGuid.ToString() == NodeGuid)
		{
			TargetNode = Node;
			break;
		}
	}
	if (!TargetNode)
	{
		ResultObject->SetStringField(TEXT("error"), FString::Printf(TEXT("Could not find node with GUID: %s"), *NodeGuid));
		FString Output;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&Output);
		FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);
		return Output;
	}

	for (UEdGraphPin* Pin : TargetNode->Pins)
	{
		if (!Pin) continue;
		TSharedPtr<FJsonObject> PinObj = MakeShareable(new FJsonObject);
		PinObj->SetStringField(TEXT("name"), Pin->PinName.ToString());
		PinObj->SetStringField(TEXT("type"), Pin->PinType.PinCategory.ToString());
		PinObj->SetStringField(TEXT("subtype"), Pin->PinType.PinSubCategory.ToString());
		PinObj->SetStringField(TEXT("direction"), Pin->Direction == EGPD_Output ? TEXT("output") : TEXT("input"));
		if (Pin->DefaultValue.Len() > 0)
			PinObj->SetStringField(TEXT("default_value"), Pin->DefaultValue);
		if (!Pin->PinFriendlyName.IsEmpty())
			PinObj->SetStringField(TEXT("friendly_name"), Pin->PinFriendlyName.ToString());
		if (Pin->bHidden)
			PinObj->SetBoolField(TEXT("hidden"), true);
		if (Pin->bNotConnectable)
			PinObj->SetBoolField(TEXT("not_connectable"), true);
		if (Pin->bDefaultValueIsReadOnly)
			PinObj->SetBoolField(TEXT("default_value_readonly"), true);
		if (Pin->bDefaultValueIsIgnored)
			PinObj->SetBoolField(TEXT("default_value_ignored"), true);

		if (Pin->Direction == EGPD_Output)
			OutputsArray.Add(MakeShareable(new FJsonValueObject(PinObj)));
		else
			InputsArray.Add(MakeShareable(new FJsonValueObject(PinObj)));
	}

	ResultObject->SetArrayField(TEXT("inputs"), InputsArray);
	ResultObject->SetArrayField(TEXT("outputs"), OutputsArray);

	FString Output;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&Output);
	FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);
	return Output;
}

FString UGenBlueprintNodeCreator::AnalyzeGraph(const FString& BlueprintPath, const FString& FunctionGuid)
{
	TSharedPtr<FJsonObject> ResultObject = MakeShareable(new FJsonObject);
	TArray<TSharedPtr<FJsonValue>> NodesArray;
	TArray<TSharedPtr<FJsonValue>> ConnectionsArray;

	UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *BlueprintPath);
	if (!Blueprint)
	{
		ResultObject->SetStringField(TEXT("error"), FString::Printf(TEXT("Could not load blueprint at path: %s"), *BlueprintPath));
		FString Output;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&Output);
		FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);
		return Output;
	}

	FGuid GraphGuid;
	if (!FGuid::Parse(FunctionGuid, GraphGuid))
	{
		ResultObject->SetStringField(TEXT("error"), FString::Printf(TEXT("Invalid function GUID: %s"), *FunctionGuid));
		FString Output;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&Output);
		FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);
		return Output;
	}

	UEdGraph* FunctionGraph = nullptr;
	for (UEdGraph* Graph : Blueprint->UbergraphPages)
		if (Graph->GraphGuid == GraphGuid)
		{
			FunctionGraph = Graph;
			break;
		}
	if (!FunctionGraph)
		for (UEdGraph* Graph : Blueprint->FunctionGraphs)
			if (Graph->GraphGuid == GraphGuid)
			{
				FunctionGraph = Graph;
				break;
			}
	if (!FunctionGraph)
	{
		ResultObject->SetStringField(TEXT("error"), FString::Printf(TEXT("Could not find function graph with GUID: %s"), *FunctionGuid));
		FString Output;
		TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&Output);
		FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);
		return Output;
	}

	// Map from pin to node GUID for connection lookup
	TMap<const UEdGraphPin*, FString> PinToNodeGuid;

	// Collect node and pin info
	for (UEdGraphNode* Node : FunctionGraph->Nodes)
	{
		if (!Node) continue;
		TSharedPtr<FJsonObject> NodeObj = MakeShareable(new FJsonObject);
		NodeObj->SetStringField(TEXT("guid"), Node->NodeGuid.ToString());
		NodeObj->SetStringField(TEXT("type"), Node->GetClass()->GetName());
		NodeObj->SetStringField(TEXT("title"), Node->GetNodeTitle(ENodeTitleType::FullTitle).ToString());
		NodeObj->SetNumberField(TEXT("pos_x"), Node->NodePosX);
		NodeObj->SetNumberField(TEXT("pos_y"), Node->NodePosY);

		TArray<TSharedPtr<FJsonValue>> InputsArray;
		TArray<TSharedPtr<FJsonValue>> OutputsArray;

		for (UEdGraphPin* Pin : Node->Pins)
		{
			if (!Pin) continue;
			PinToNodeGuid.Add(Pin, Node->NodeGuid.ToString());
			TSharedPtr<FJsonObject> PinObj = MakeShareable(new FJsonObject);
			PinObj->SetStringField(TEXT("name"), Pin->PinName.ToString());
			PinObj->SetStringField(TEXT("type"), Pin->PinType.PinCategory.ToString());
			PinObj->SetStringField(TEXT("subtype"), Pin->PinType.PinSubCategory.ToString());
			PinObj->SetStringField(TEXT("direction"), Pin->Direction == EGPD_Output ? TEXT("output") : TEXT("input"));
			if (Pin->DefaultValue.Len() > 0)
				PinObj->SetStringField(TEXT("default_value"), Pin->DefaultValue);
			if (!Pin->PinFriendlyName.IsEmpty())
				PinObj->SetStringField(TEXT("friendly_name"), Pin->PinFriendlyName.ToString());
			if (Pin->bHidden)
				PinObj->SetBoolField(TEXT("hidden"), true);
			if (Pin->bNotConnectable)
				PinObj->SetBoolField(TEXT("not_connectable"), true);
			if (Pin->bDefaultValueIsReadOnly)
				PinObj->SetBoolField(TEXT("default_value_readonly"), true);
			if (Pin->bDefaultValueIsIgnored)
				PinObj->SetBoolField(TEXT("default_value_ignored"), true);

			if (Pin->Direction == EGPD_Output)
				OutputsArray.Add(MakeShareable(new FJsonValueObject(PinObj)));
			else
				InputsArray.Add(MakeShareable(new FJsonValueObject(PinObj)));
		}
		NodeObj->SetArrayField(TEXT("inputs"), InputsArray);
		NodeObj->SetArrayField(TEXT("outputs"), OutputsArray);
		NodesArray.Add(MakeShareable(new FJsonValueObject(NodeObj)));
	}

	// Collect connections
	for (UEdGraphNode* Node : FunctionGraph->Nodes)
	{
		if (!Node) continue;
		for (UEdGraphPin* Pin : Node->Pins)
		{
			if (!Pin) continue;
			if (Pin->Direction == EGPD_Output)
			{
				for (UEdGraphPin* LinkedPin : Pin->LinkedTo)
				{
					if (!LinkedPin) continue;
					TSharedPtr<FJsonObject> ConnObj = MakeShareable(new FJsonObject);
					ConnObj->SetStringField(TEXT("source_node"), Node->NodeGuid.ToString());
					ConnObj->SetStringField(TEXT("source_pin"), Pin->PinName.ToString());
					if (PinToNodeGuid.Contains(LinkedPin))
					{
						ConnObj->SetStringField(TEXT("target_node"), PinToNodeGuid[LinkedPin]);
					}
					else
					{
						ConnObj->SetStringField(TEXT("target_node"), TEXT("unknown"));
					}
					ConnObj->SetStringField(TEXT("target_pin"), LinkedPin->PinName.ToString());
					ConnectionsArray.Add(MakeShareable(new FJsonValueObject(ConnObj)));
				}
			}
		}
	}

	ResultObject->SetArrayField(TEXT("nodes"), NodesArray);
	ResultObject->SetArrayField(TEXT("connections"), ConnectionsArray);

	FString Output;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&Output);
	FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);
	return Output;
}

FString UGenBlueprintNodeCreator::GetCurrentFocusedGraph()
{
	TSharedPtr<FJsonObject> ResultObject = MakeShareable(new FJsonObject);
	
	// For now, return a placeholder response indicating this feature is not yet implemented
	// This function would require more complex editor integration that may vary between UE versions
	ResultObject->SetStringField(TEXT("error"), TEXT("GetCurrentFocusedGraph not yet implemented for this UE version"));
	ResultObject->SetStringField(TEXT("message"), TEXT("Use explicit blueprint_path and function_id parameters instead"));
	
	FString Output;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&Output);
	FJsonSerializer::Serialize(ResultObject.ToSharedRef(), Writer);
	return Output;
}

