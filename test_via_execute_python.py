#!/usr/bin/env python3
"""
Test the C++ function by sending it via execute_python command.
"""

import json
import socket
import time

def test_via_execute_python():
    """Test the C++ function via execute_python command."""
    
    print("🧪 Testing C++ via execute_python")
    print("=" * 35)
    
    try:
        # Connect to Unreal Engine
        print("🔌 Connecting to Unreal Engine...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(60)
        sock.connect(('127.0.0.1', 9877))
        print("✅ Connected!")
        
        # Create the Python code to test the C++ function
        python_code = '''
import unreal
import json

print("🚨 PYTHON: Starting direct C++ test...")

try:
    # Check if the class exists
    print("🚨 PYTHON: Checking GenBlueprintNodeCreator class...")
    node_creator = unreal.GenBlueprintNodeCreator
    print(f"🚨 PYTHON: Class found: {node_creator}")
    
    # Check if the method exists
    print("🚨 PYTHON: Checking add_nodes_bulk method...")
    method = getattr(node_creator, 'add_nodes_bulk', None)
    if method:
        print(f"🚨 PYTHON: Method found: {method}")
    else:
        print("🚨 PYTHON: Method not found!")
        
    # Try to create a function first using the correct class
    print("🚨 PYTHON: Creating test function...")
    blueprint_path = "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson"
    function_name = "PythonDirectTest"

    # Use GenBlueprintUtils for function creation, not GenBlueprintNodeCreator
    gen_bp_utils = unreal.GenBlueprintUtils
    function_id = gen_bp_utils.add_function(blueprint_path, function_name, "[]", "[]")
    print(f"🚨 PYTHON: Function created with ID: {function_id}")
    
    # Convert GUID format
    formatted_function_id = function_id
    if len(function_id) == 32 and '-' not in function_id:
        formatted_function_id = f"{function_id[:8]}-{function_id[8:12]}-{function_id[12:16]}-{function_id[16:20]}-{function_id[20:]}"
        print(f"🚨 PYTHON: Converted to: {formatted_function_id}")
    
    # Create minimal node data
    nodes_data = [
        {
            "node_type": "PrintString",
            "node_id": "test1", 
            "node_position": [0, 0],
            "node_properties": {}
        }
    ]
    
    nodes_json = json.dumps(nodes_data)
    print(f"🚨 PYTHON: About to call add_nodes_bulk...")
    print(f"🚨 PYTHON: Blueprint: {blueprint_path}")
    print(f"🚨 PYTHON: Function ID: {formatted_function_id}")
    print(f"🚨 PYTHON: Nodes JSON: {nodes_json}")
    
    # This is where the crash should happen if there's an issue
    result = node_creator.add_nodes_bulk(blueprint_path, formatted_function_id, nodes_json)
    
    print(f"🚨 PYTHON: SUCCESS! Result: {result}")
    
except Exception as e:
    print(f"🚨 PYTHON: ERROR: {e}")
    import traceback
    traceback.print_exc()
'''
        
        # Send the Python code via execute_python
        print("📤 Sending Python code via execute_python...")
        execute_request = {
            "type": "execute_python",
            "script": python_code
        }
        
        request_json = json.dumps(execute_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        print("⏳ Waiting for response...")
        
        try:
            response_data = sock.recv(16384).decode('utf-8')
            print(f"📥 Raw response: {response_data}")
            
            response = json.loads(response_data)
            print(f"📊 Parsed response: {response}")
            
            if response.get("success"):
                print("✅ execute_python succeeded!")
                output = response.get("output", "")
                if output:
                    print("📋 Python output:")
                    print(output)
                return True
            else:
                print(f"❌ execute_python failed: {response}")
                return False
                
        except socket.error as e:
            print(f"❌ Socket error (connection aborted): {e}")
            print("   This indicates the C++ function crashed")
            return False
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"   Raw response was: {response_data}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    test_via_execute_python()
