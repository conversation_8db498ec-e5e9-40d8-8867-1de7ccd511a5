#!/usr/bin/env python3
"""
Check if the socket server is running and responsive.
"""

import socket
import time

def check_socket():
    """Check if socket server is running."""
    
    print("🔍 Checking if socket server is running...")
    
    try:
        # Try IPv6 first
        print("🔌 Trying IPv6 connection (::1:9877)...")
        sock = socket.socket(socket.AF_INET6, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect(('::1', 9877))
        print("✅ IPv6 connection successful!")
        sock.close()
        return True
    except Exception as e:
        print(f"❌ IPv6 failed: {e}")
    
    try:
        # Try IPv4
        print("🔌 Trying IPv4 connection (127.0.0.1:9877)...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect(('127.0.0.1', 9877))
        print("✅ IPv4 connection successful!")
        sock.close()
        return True
    except Exception as e:
        print(f"❌ IPv4 failed: {e}")
    
    print("❌ No socket server found on port 9877")
    return False

if __name__ == "__main__":
    check_socket()
