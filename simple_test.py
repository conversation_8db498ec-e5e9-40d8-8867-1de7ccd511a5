#!/usr/bin/env python3
"""
Simple test to check if we can connect to Unreal Engine and get a response.
"""

import json
import socket
import time

def simple_test():
    """Simple test to verify connection and basic functionality."""
    
    print("🧪 Simple CreateLex Test")
    print("=" * 30)
    
    try:
        # Connect to Unreal Engine
        print("🔌 Connecting to Unreal Engine...")
        sock = socket.socket(socket.AF_INET6, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('::1', 9877))
        print("✅ Connected!")
        
        # Send a simple test request
        test_request = {
            "type": "generate_smart_blueprint_function",
            "function_name": "TestFunction",
            "description": "Simple test function",
            "inputs": [{"name": "Input1", "type": "float"}],
            "outputs": [{"name": "Output1", "type": "float"}],
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson"
        }
        
        print("📤 Sending request...")
        request_json = json.dumps(test_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        print("📥 Waiting for response...")
        response_data = sock.recv(4096).decode('utf-8')
        sock.close()
        
        print("✅ Response received:")
        print(response_data[:200] + "..." if len(response_data) > 200 else response_data)
        
        # Try to parse as JSON
        try:
            response = json.loads(response_data)
            print(f"📊 Success: {response.get('success', 'Unknown')}")
            if 'connections_made' in response:
                print(f"🔗 Connections made: {response['connections_made']}")
            return True
        except json.JSONDecodeError:
            print("⚠️ Response is not JSON, but connection worked")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    simple_test()
