#!/usr/bin/env python3
"""
Test script to verify the immediate return functionality of AddNodesBulk C++ function.
This tests our fix for the unreachable code compilation errors.
"""

import socket
import json
import time

def test_immediate_return():
    """Test the immediate return functionality"""
    print("🚀 Testing AddNodesBulk immediate return functionality...")
    
    try:
        # Connect to Unreal Engine socket server
        print("📡 Connecting to Unreal Engine on localhost:9877...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('localhost', 9877))
        print("✅ Connected successfully!")
        
        # Prepare test command - use "type" not "command" and "function_id" not "function_guid"
        test_command = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/TestBlueprint",
            "function_id": "test-guid-12345",
            "nodes": [
                {
                    "node_type": "PrintString",
                    "node_id": "test_node_1",
                    "position": {"x": 100, "y": 200}
                }
            ]
        }
        
        print("📤 Sending test command...")
        print(f"Command: {json.dumps(test_command, indent=2)}")
        
        # Send command
        message = json.dumps(test_command) + '\n'
        sock.sendall(message.encode('utf-8'))
        
        print("⏳ Waiting for response...")
        
        # Receive response
        response_data = b''
        while True:
            chunk = sock.recv(4096)
            if not chunk:
                break
            response_data += chunk
            if b'\n' in response_data:
                break
        
        response_str = response_data.decode('utf-8').strip()
        print(f"📥 Raw response: {response_str}")
        
        # Parse response
        try:
            response = json.loads(response_str)
            print(f"📋 Parsed response: {json.dumps(response, indent=2)}")
            
            # Check for successful response (empty nodes array means C++ function worked)
            if response.get('success') and 'nodes' in response:
                print("🎉 SUCCESS! Immediate return test PASSED!")
                print("✅ C++ function is being called without crashing")
                print("✅ Unreachable code compilation errors are fixed")
                print("✅ Python handler processes C++ response correctly")
                print(f"✅ Response format: {response}")
                return True
            else:
                print("❌ UNEXPECTED RESPONSE FORMAT")
                print(f"Expected: success=True with 'nodes' field")
                print(f"Got: {response}")
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {e}")
            print(f"Raw response was: {response_str}")
            return False
            
    except socket.timeout:
        print("❌ TIMEOUT: No response from Unreal Engine")
        print("💡 Make sure Unreal Engine is running with the CreatelexGenAI plugin loaded")
        return False
        
    except ConnectionRefusedError:
        print("❌ CONNECTION REFUSED: Cannot connect to localhost:9877")
        print("💡 Make sure Unreal Engine is running and the socket server is started")
        return False
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False
        
    finally:
        try:
            sock.close()
        except:
            pass

def main():
    print("🧪 IMMEDIATE RETURN TEST - CreatelexGenAI Plugin")
    print("=" * 60)
    print("This test verifies that our C++ AddNodesBulk function fix works.")
    print("Expected: Function returns success message without crashing.")
    print("=" * 60)
    
    success = test_immediate_return()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 TEST RESULT: PASSED ✅")
        print("🔧 Next step: Implement full FGraphNodeCreator pattern")
    else:
        print("🎯 TEST RESULT: FAILED ❌")
        print("🔧 Next step: Debug the C++ function or connection")
    print("=" * 60)

if __name__ == "__main__":
    main()
