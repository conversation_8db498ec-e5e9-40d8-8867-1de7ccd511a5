#!/usr/bin/env python3
"""
Test simple Blueprint commands to isolate the hang.
"""

import json
import socket
import time

def test_blueprint_commands():
    """Test various Blueprint commands to find where the hang occurs."""
    
    print("🧪 Testing Blueprint Commands")
    print("=" * 40)
    
    # Test commands in order of complexity
    test_commands = [
        {
            "name": "get_unreal_context",
            "command": {"type": "get_unreal_context"}
        },
        {
            "name": "get_current_blueprint_context", 
            "command": {"type": "get_current_blueprint_context"}
        },
        {
            "name": "spawn_object",
            "command": {
                "type": "spawn", 
                "object_type": "Cube",
                "location": [0, 0, 100]
            }
        }
    ]
    
    for test in test_commands:
        try:
            print(f"\n🔌 Testing {test['name']}...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(15)
            sock.connect(('127.0.0.1', 9877))
            
            request_json = json.dumps(test['command']) + '\n'
            sock.send(request_json.encode('utf-8'))
            
            print("📥 Waiting for response...")
            response_data = sock.recv(4096).decode('utf-8')
            sock.close()
            
            print(f"✅ {test['name']} responded!")
            print(f"📊 Response length: {len(response_data)} chars")
            
            # Try to parse as JSON to check if it's valid
            try:
                response = json.loads(response_data)
                print(f"📊 Success: {response.get('success', 'Unknown')}")
                if 'error' in response:
                    print(f"❌ Error: {response['error']}")
            except json.JSONDecodeError:
                print("⚠️ Response is not valid JSON")
                print(f"📄 Raw response: {response_data[:200]}...")
            
        except Exception as e:
            print(f"❌ {test['name']} failed: {e}")

if __name__ == "__main__":
    test_blueprint_commands()
