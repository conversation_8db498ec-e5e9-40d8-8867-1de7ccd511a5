#!/usr/bin/env python3
"""
Test manual connection creation to isolate the issue.
"""

import json
import socket
import time

def test_manual_connection():
    """Test manual connection creation step by step."""
    
    print("🧪 Testing Manual Connection")
    print("=" * 30)
    
    try:
        # Connect to Unreal Engine
        print("🔌 Connecting to Unreal Engine...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(60)
        sock.connect(('127.0.0.1', 9877))
        print("✅ Connected!")
        
        # First, create a function
        print("📤 Creating function...")
        create_request = {
            "type": "add_function",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_name": "ManualTest",
            "inputs": [{"name": "A", "type": "float"}, {"name": "B", "type": "float"}],
            "outputs": [{"name": "Sum", "type": "float"}]
        }
        
        request_json = json.dumps(create_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        response_data = sock.recv(8192).decode('utf-8')
        response = json.loads(response_data)
        
        if not response.get("success"):
            print(f"❌ Function creation failed: {response}")
            return False
            
        function_id = response.get("function_id")
        print(f"✅ Function created with ID: {function_id}")
        
        # Now add nodes manually
        print("📤 Adding nodes...")
        nodes_request = {
            "type": "add_nodes_bulk",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_id": function_id,
            "nodes": [
                {
                    "node_type": "Add_DoubleDouble",
                    "node_id": "add_node",
                    "node_position": [100, 100],
                    "node_properties": {}
                },
                {
                    "node_type": "PrintString",
                    "node_id": "print_node",
                    "node_position": [300, 100],
                    "node_properties": {}
                }
            ]
        }
        
        request_json = json.dumps(nodes_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        response_data = sock.recv(8192).decode('utf-8')
        response = json.loads(response_data)
        
        if not response.get("success"):
            print(f"❌ Node creation failed: {response}")
            return False
            
        print(f"✅ Nodes created successfully")
        
        # Parse the node mapping
        created_nodes = response.get("created_nodes", [])
        node_mapping = {}
        for node in created_nodes:
            if isinstance(node, dict):
                ref_id = node.get("ref_id")
                node_guid = node.get("node_guid")
                if ref_id and node_guid:
                    node_mapping[ref_id] = node_guid
                    
        print(f"🗺️ Node mapping: {node_mapping}")
        
        if len(node_mapping) < 2:
            print("❌ Not enough nodes created for connection test")
            return False
            
        # Now test a simple connection
        print("📤 Testing simple connection...")
        connections_request = {
            "type": "connect_nodes_bulk",
            "blueprint_path": "/Game/ThirdPerson/Lvl_ThirdPerson.Lvl_ThirdPerson:PersistentLevel.Lvl_ThirdPerson",
            "function_id": function_id,
            "connections": [
                {
                    "source_node_id": node_mapping["add_node"],
                    "source_pin": "ReturnValue",
                    "target_node_id": node_mapping["print_node"],
                    "target_pin": "InString"
                }
            ]
        }
        
        request_json = json.dumps(connections_request) + '\n'
        sock.send(request_json.encode('utf-8'))
        
        response_data = sock.recv(8192).decode('utf-8')
        response = json.loads(response_data)
        
        print(f"📊 Connection response: {response}")
        
        if response.get("success"):
            connections_made = response.get("successful_connections", 0)
            print(f"✅ Connections made: {connections_made}")
            if connections_made > 0:
                print("🎉 SUCCESS! Connection system is working!")
                return True
            else:
                print("⚠️ Connection command succeeded but no connections were made")
                return False
        else:
            print(f"❌ Connection failed: {response}")
            return False
            
        sock.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_manual_connection()
